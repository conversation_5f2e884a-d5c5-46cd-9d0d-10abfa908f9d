"""
Level 2数据盘口压单分析模块
基于原有盘口压单代码，适配1秒级Level 2数据

核心功能：
1. 计算买卖盘阻力
2. 识别盘口压单信号
3. 评估信号收益
"""

import pandas as pd
import numpy as np
import json
from datetime import datetime, timedelta
import warnings
warnings.filterwarnings('ignore')

class Level2PlatePressureAnalyzer:
    
    def __init__(self, data_df, plate_percent=0.002, max_depth=10):
        """
        初始化Level 2盘口压单分析器
        
        Args:
            data_df: Level 2数据DataFrame
            plate_percent: 盘口压单价格宽度百分比，默认0.2%
            max_depth: 最大档位深度，默认10档
        """
        self.data_df = data_df.copy()
        self.plate_percent = plate_percent
        self.max_depth = max_depth
        self.point = 0.000001
        
        # 处理后的数据
        self.processed_data = []
        self.width = 0
        
        # 阻力数据
        self.long_resist_list = []  # 卖盘阻力（做多时的阻力）
        self.short_resist_list = []  # 买盘阻力（做空时的阻力）
        
        # 信号数据
        self.long_plate_signals = []  # 多头压单信号
        self.short_plate_signals = []  # 空头压单信号
        
        # 初始化处理
        self._preprocess_data()
    
    def _preprocess_data(self):
        """预处理Level 2数据"""
        print("开始预处理Level 2数据...")
        
        # 转换时间戳
        self.data_df['timestamp_dt'] = pd.to_datetime(self.data_df['timestamp'])
        self.data_df = self.data_df.sort_values('timestamp_dt').reset_index(drop=True)
        
        # 解析JSON格式的买卖盘数据
        for idx, row in self.data_df.iterrows():
            try:
                # 解析买卖盘JSON数据
                bids_data = json.loads(row['bids_json']) if isinstance(row['bids_json'], str) else row['bids_json']
                asks_data = json.loads(row['asks_json']) if isinstance(row['asks_json'], str) else row['asks_json']
                
                # 提取价格和数量，限制到max_depth档
                bid_prices = [float(bid[0]) for bid in bids_data[:self.max_depth]]
                bid_volumes = [float(bid[1]) for bid in bids_data[:self.max_depth]]
                ask_prices = [float(ask[0]) for ask in asks_data[:self.max_depth]]
                ask_volumes = [float(ask[1]) for ask in asks_data[:self.max_depth]]
                
                # 补齐到max_depth档（如果数据不足）
                while len(bid_prices) < self.max_depth:
                    bid_prices.append(0.0)
                    bid_volumes.append(0.0)
                while len(ask_prices) < self.max_depth:
                    ask_prices.append(0.0)
                    ask_volumes.append(0.0)
                
                processed_tick = {
                    'timestamp': row['timestamp_dt'],
                    'symbol': row['symbol'],
                    'bid_prices': bid_prices,
                    'bid_volumes': bid_volumes,
                    'ask_prices': ask_prices,
                    'ask_volumes': ask_volumes,
                    'best_bid': float(row['best_bid_price']),
                    'best_ask': float(row['best_ask_price']),
                    'mid_price': (float(row['best_bid_price']) + float(row['best_ask_price'])) / 2,
                    'spread': float(row['spread'])
                }
                
                self.processed_data.append(processed_tick)
                
            except Exception as e:
                print(f"处理第{idx}行数据时出错: {e}")
                continue
        
        print(f"成功处理{len(self.processed_data)}条数据")
    
    def _calculate_rational_width(self):
        """计算合理的价格宽度"""
        if not self.processed_data:
            return
        
        # 使用第一个tick的中间价计算宽度
        first_tick = self.processed_data[0]
        mid_price = first_tick['mid_price']
        
        # 计算价格宽度，向上取整到分
        self.width = np.ceil(mid_price * self.plate_percent * 100) / 100
        print(f"计算得到价格宽度: {self.width}")
    
    def _long_resist(self, ask_prices, ask_volumes):
        """
        计算卖盘阻力（做多时的阻力）
        在ask_prices[0] + width范围内的卖量总和
        """
        resist = 0
        base_price = ask_prices[0]
        
        for i in range(len(ask_prices)):
            price = ask_prices[i]
            volume = ask_volumes[i]
            
            # 价格在有效范围内且大于0
            if price < base_price + self.width + self.point and price > self.point:
                resist += volume
        
        return resist
    
    def _short_resist(self, bid_prices, bid_volumes):
        """
        计算买盘阻力（做空时的阻力）
        在bid_prices[0] - width范围内的买量总和
        """
        resist = 0
        base_price = bid_prices[0]
        
        for i in range(len(bid_prices)):
            price = bid_prices[i]
            volume = bid_volumes[i]
            
            # 价格在有效范围内且大于0
            if price > base_price - self.width - self.point and price > self.point:
                resist += volume
        
        return resist
    
    def calculate_resistance(self):
        """计算所有tick的买卖盘阻力"""
        print("开始计算买卖盘阻力...")
        
        # 计算价格宽度
        self._calculate_rational_width()
        
        self.long_resist_list = []
        self.short_resist_list = []
        
        for tick in self.processed_data:
            # 计算卖盘阻力（做多阻力）
            long_resist = self._long_resist(tick['ask_prices'], tick['ask_volumes'])
            self.long_resist_list.append(long_resist)
            
            # 计算买盘阻力（做空阻力）
            short_resist = self._short_resist(tick['bid_prices'], tick['bid_volumes'])
            self.short_resist_list.append(short_resist)
        
        print(f"计算完成，共{len(self.long_resist_list)}个阻力值")
    
    def generate_plate_signals(self, ratio_low=2.0, ratio_high=10.0):
        """
        生成盘口压单信号
        
        Args:
            ratio_low: 压单比例下限
            ratio_high: 压单比例上限
        """
        print(f"生成压单信号，比例范围: {ratio_low} - {ratio_high}")
        
        if not self.long_resist_list or not self.short_resist_list:
            print("请先计算阻力值")
            return
        
        self.long_plate_signals = []
        self.short_plate_signals = []
        
        for i in range(len(self.long_resist_list)):
            long_resist = self.long_resist_list[i]
            short_resist = self.short_resist_list[i]
            
            # 多头压单信号：买盘阻力 >= 卖盘阻力 * ratio_low 且 <= 卖盘阻力 * ratio_high
            if (long_resist > self.point and 
                short_resist >= long_resist * ratio_low and 
                short_resist <= long_resist * ratio_high):
                self.long_plate_signals.append(1)
            else:
                self.long_plate_signals.append(0)
            
            # 空头压单信号：卖盘阻力 >= 买盘阻力 * ratio_low 且 <= 买盘阻力 * ratio_high
            if (short_resist > self.point and 
                long_resist >= short_resist * ratio_low and 
                long_resist <= short_resist * ratio_high):
                self.short_plate_signals.append(1)
            else:
                self.short_plate_signals.append(0)
        
        long_signal_count = sum(self.long_plate_signals)
        short_signal_count = sum(self.short_plate_signals)
        
        print(f"生成信号完成:")
        print(f"  多头压单信号: {long_signal_count} 个")
        print(f"  空头压单信号: {short_signal_count} 个")
        print(f"  总信号率: {(long_signal_count + short_signal_count) / len(self.long_resist_list):.2%}")
    
    def calculate_signal_performance(self, twap_duration=120, signal_duration=60):
        """
        计算信号表现
        
        Args:
            twap_duration: TWAP计算持续时间（秒）
            signal_duration: 信号最小间隔时间（秒）
        """
        print(f"计算信号表现，TWAP持续时间: {twap_duration}秒，信号间隔: {signal_duration}秒")
        
        if not self.long_plate_signals or not self.short_plate_signals:
            print("请先生成压单信号")
            return None, None
        
        # 计算多头信号表现
        long_profits = self._calculate_signal_profits(
            self.long_plate_signals, twap_duration, signal_duration, is_long=True
        )
        
        # 计算空头信号表现
        short_profits = self._calculate_signal_profits(
            self.short_plate_signals, twap_duration, signal_duration, is_long=False
        )
        
        return long_profits, short_profits
    
    def _calculate_signal_profits(self, signal_list, twap_duration, signal_duration, is_long=True):
        """计算信号收益列表"""
        profit_list = []
        position = 0
        start_time = None
        
        for i in range(len(self.processed_data)):
            current_time = self.processed_data[i]['timestamp']
            
            if position == 0:
                # 检查是否还有足够时间计算TWAP
                if i >= len(self.processed_data) - 1:
                    break
                
                remaining_time = (self.processed_data[-1]['timestamp'] - current_time).total_seconds()
                if remaining_time < twap_duration:
                    break
                
                # 检查是否有信号
                if signal_list[i] > self.point:
                    start_time = current_time
                    position = 1
                    
                    # 计算该信号的收益
                    profit = self._calculate_twap_profit(i, twap_duration, is_long)
                    profit_list.append(profit)
            
            else:
                # 检查是否超过信号间隔时间
                if start_time and (current_time - start_time).total_seconds() > signal_duration - self.point:
                    position = 0
        
        return profit_list
    
    def _calculate_twap_profit(self, signal_index, duration, is_long=True):
        """计算单个信号的TWAP收益"""
        signal_tick = self.processed_data[signal_index]
        signal_time = signal_tick['timestamp']
        
        # 交易价格（买卖中间价）
        trade_price = signal_tick['mid_price']
        
        # 收集TWAP价格
        twap_prices = [trade_price]
        
        for j in range(signal_index + 1, len(self.processed_data)):
            tick = self.processed_data[j]
            time_diff = (tick['timestamp'] - signal_time).total_seconds()
            
            if time_diff >= duration:
                break
            
            twap_prices.append(tick['mid_price'])
        
        # 计算TWAP
        twap = np.mean(twap_prices)
        
        # 计算收益
        if is_long:
            profit = twap - trade_price  # 做多收益
        else:
            profit = trade_price - twap  # 做空收益
        
        # 返回基点收益
        return profit / twap * 10000.0
    
    def get_analysis_results(self):
        """获取分析结果"""
        if not self.long_resist_list:
            return None
        
        results = {
            'total_ticks': len(self.processed_data),
            'price_width': self.width,
            'long_resist_stats': {
                'mean': np.mean(self.long_resist_list),
                'std': np.std(self.long_resist_list),
                'max': np.max(self.long_resist_list),
                'min': np.min(self.long_resist_list)
            },
            'short_resist_stats': {
                'mean': np.mean(self.short_resist_list),
                'std': np.std(self.short_resist_list),
                'max': np.max(self.short_resist_list),
                'min': np.min(self.short_resist_list)
            }
        }
        
        if self.long_plate_signals:
            results['signal_stats'] = {
                'long_signals': sum(self.long_plate_signals),
                'short_signals': sum(self.short_plate_signals),
                'total_signals': sum(self.long_plate_signals) + sum(self.short_plate_signals),
                'signal_rate': (sum(self.long_plate_signals) + sum(self.short_plate_signals)) / len(self.long_plate_signals)
            }
        
        return results
    
    def export_results(self, filename=None):
        """导出结果到CSV"""
        if filename is None:
            timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
            filename = f"plate_pressure_results_{timestamp}.csv"
        
        # 创建结果DataFrame
        results_data = []
        
        for i, tick in enumerate(self.processed_data):
            row = {
                'timestamp': tick['timestamp'],
                'symbol': tick['symbol'],
                'mid_price': tick['mid_price'],
                'best_bid': tick['best_bid'],
                'best_ask': tick['best_ask'],
                'spread': tick['spread'],
                'long_resist': self.long_resist_list[i] if i < len(self.long_resist_list) else 0,
                'short_resist': self.short_resist_list[i] if i < len(self.short_resist_list) else 0,
                'long_signal': self.long_plate_signals[i] if i < len(self.long_plate_signals) else 0,
                'short_signal': self.short_plate_signals[i] if i < len(self.short_plate_signals) else 0
            }
            
            # 添加阻力比例
            if row['long_resist'] > 0:
                row['resist_ratio_short_to_long'] = row['short_resist'] / row['long_resist']
            else:
                row['resist_ratio_short_to_long'] = 0
            
            if row['short_resist'] > 0:
                row['resist_ratio_long_to_short'] = row['long_resist'] / row['short_resist']
            else:
                row['resist_ratio_long_to_short'] = 0
            
            results_data.append(row)
        
        results_df = pd.DataFrame(results_data)
        results_df.to_csv(filename, index=False, encoding='utf-8')
        print(f"结果已导出到: {filename}")
        
        return filename
