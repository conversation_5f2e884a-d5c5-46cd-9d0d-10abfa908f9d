# 1秒动量因子分析系统

基于Level 2数据的1秒动量因子计算和盘口显示系统，从3秒动量因子演化而来，专门处理高频交易数据。

## 文件结构

```
3秒动量因子/
├── one_second_momentum_processor.py    # 1秒动量计算核心模块
├── level2_orderbook_display.py        # Level 2盘口显示模块
├── main_level2_analysis.py            # 主程序入口
├── README_1秒动量.md                   # 本说明文件
└── 原有3秒动量文件...
```

## 功能特性

### 1. 1秒动量计算 (`one_second_momentum_processor.py`)
- **动量算法**: 基于买卖盘量变化计算1秒级动量
- **价格范围**: 动态计算价格宽度范围
- **信号生成**: 自动生成多空交易信号
- **数据导出**: 支持CSV格式结果导出

### 2. Level 2盘口显示 (`level2_orderbook_display.py`)
- **实时盘口**: 显示多档买卖盘深度
- **可视化图表**: 盘口深度图、价格走势图
- **动量展示**: 集成动量信号显示
- **统计报告**: 自动生成分析报告

### 3. 主程序 (`main_level2_analysis.py`)
- **一键分析**: 整合所有分析功能
- **配置灵活**: 支持参数自定义
- **批量处理**: 支持大量数据处理
- **结果保存**: 自动保存图表和数据

## 数据格式要求

输入数据需包含以下列：

| 列名 | 类型 | 说明 |
|------|------|------|
| id | int | 记录ID |
| symbol | str | 交易品种 |
| timestamp | str | 时间戳 |
| update_id | int | 更新ID |
| bids_json | str | 买盘JSON数据 `[[价格, 数量], ...]` |
| asks_json | str | 卖盘JSON数据 `[[价格, 数量], ...]` |
| best_bid_price | float | 最优买价 |
| best_ask_price | float | 最优卖价 |
| bid_volume | float | 总买量 |
| ask_volume | float | 总卖量 |
| spread | float | 价差 |
| spread_percent | float | 价差百分比 |
| created_at | str | 创建时间 |

## 使用方法

### 🚀 快速生成CSV结果

#### 方法1：使用专用脚本（推荐）
```bash
# 使用样本数据
python run_analysis_csv.py

# 使用自己的数据文件
python run_analysis_csv.py your_level2_data.csv
```

#### 方法2：使用主程序
```bash
# 使用样本数据
python main_level2_analysis.py --demo --output results/

# 使用数据文件
python main_level2_analysis.py --data your_data.csv --output results/
```

### 1. 快速开始（使用样本数据）

```python
from main_level2_analysis import Level2AnalysisMain, create_sample_data

# 创建样本数据
sample_df = create_sample_data()

# 初始化分析器
analyzer = Level2AnalysisMain(sample_df)

# 运行完整分析
results = analyzer.run_full_analysis()

# 显示盘口快照
analyzer.display_sample_orderbook(0)
```

### 2. 使用CSV文件

```python
from main_level2_analysis import Level2AnalysisMain

# 从文件加载数据
analyzer = Level2AnalysisMain('your_level2_data.csv')

# 运行分析
results = analyzer.run_full_analysis()
```

### 3. 命令行使用

```bash
# 使用样本数据演示
python main_level2_analysis.py --demo

# 使用自己的数据文件
python main_level2_analysis.py --data your_data.csv --output results/

# 查看帮助
python main_level2_analysis.py --help
```

### 4. 单独使用各模块

#### 只计算动量
```python
from one_second_momentum_processor import OneSecondMomentumProcessor
import pandas as pd

# 加载数据
df = pd.read_csv('your_data.csv')

# 计算动量
processor = OneSecondMomentumProcessor(df)
signals = processor.calculate_momentum_signals()

# 获取统计
stats = processor.get_signal_statistics()
print(stats)
```

#### 只显示盘口
```python
from level2_orderbook_display import Level2OrderBookDisplay
import pandas as pd

# 加载数据
df = pd.read_csv('your_data.csv')

# 创建显示器
display = Level2OrderBookDisplay(df)

# 显示盘口
display.display_current_orderbook(0)

# 绘制图表
display.plot_orderbook_depth(0)
display.plot_price_momentum_timeline()
```

## 配置参数

可以通过配置字典自定义参数：

```python
config = {
    'momentum_window': 1.0,          # 动量计算窗口(秒)
    'price_width_percent': 0.001,    # 价格宽度百分比
    'momentum_threshold': 0.01,      # 动量信号阈值
    'max_depth': 10,                 # 最大显示档位
    'output_dir': 'output',          # 输出目录
    'save_plots': True,              # 是否保存图表
    'save_results': True             # 是否保存结果
}

analyzer = Level2AnalysisMain(data, config)
```

## 输出结果

### 1. 核心CSV文件

#### `momentum_results_*.csv` - 动量信号文件
包含以下列：
- timestamp: 时间戳
- symbol: 品种
- mid_price: 中间价
- momentum: 动量值
- long_signal: 多头信号(0/1)
- short_signal: 空头信号(0/1)

#### `detailed_analysis_*.csv` - 详细分析文件
包含以下列：
- timestamp: 时间戳
- symbol: 品种
- mid_price: 中间价
- momentum_value: 动量值
- long_signal: 多头信号
- short_signal: 空头信号
- signal_type: 信号类型(Long/Short/None)
- abs_momentum: 绝对动量值
- price_change: 价格变化
- price_change_pct: 价格变化百分比
- momentum_rank: 动量排名(百分位)

#### `summary_stats_*.csv` - 统计汇总文件
包含以下列：
- metric: 指标名称
- value: 指标值
- category: 指标分类(momentum/signal/price/spread/other)

### 2. 可视化图表
- **盘口深度图**: 显示买卖盘深度分布
- **价格走势图**: 显示价格、价差、动量时间序列
- **交易信号图**: 显示多空信号分布

### 3. 文本报告
- `analysis_summary_*.txt`: 完整的分析报告
- 价格统计：均值、标准差、范围
- 动量统计：信号数量、信号率
- 盘口统计：深度分析

## 算法原理

### 1秒动量计算公式：
```
动量 = (当前买量增加 + 当前卖量减少) / 总阻力
```

其中：
- 买量增加 = 当前tick价格范围内买量 - 前一tick价格范围内买量
- 卖量减少 = 前一tick价格范围内卖量 - 当前tick价格范围内卖量
- 总阻力 = 当前tick价格范围内总买卖量

### 信号生成：
- 多头信号：动量 >= 阈值
- 空头信号：动量 <= -阈值
- 无信号：|动量| < 阈值

## 依赖库

```bash
pip install pandas numpy matplotlib seaborn
```

## 注意事项

1. **数据质量**: 确保Level 2数据完整且格式正确
2. **时间序列**: 数据应按时间顺序排列
3. **内存使用**: 大量数据可能需要较多内存
4. **参数调优**: 根据具体品种调整动量阈值和价格宽度

## 扩展功能

可以基于现有框架扩展：
- 多品种同时分析
- 实时数据流处理
- 更复杂的动量算法
- 机器学习信号优化

## 与3秒动量的区别

| 特性 | 3秒动量 | 1秒动量 |
|------|---------|---------|
| 时间窗口 | 3秒 | 1秒 |
| 数据源 | Tick数据 | Level 2数据 |
| 精度 | 较低 | 更高 |
| 信号频率 | 较低 | 更高 |
| 计算复杂度 | 较低 | 较高 |

## 示例输出

```
============================================================
时间: 2025-04-27 08:02:03
品种: SOLUSDT
价差: 0.0100 (0.0067%)
1秒动量: 0.001234
多头信号: 1, 空头信号: 0
============================================================
档位 卖量         卖价       买价       买量        
------------------------------------------------------------
1    52.70        149.15     149.14     1014.48     
2    159.86       149.16     149.13     883.52      
3    199.03       149.17     149.12     708.73      
4    374.12       149.18     149.11     1385.65     
5    407.73       149.19     149.10     850.55      
```

## 技术支持

如有问题或建议，请参考原有3秒动量因子代码或联系开发团队。
