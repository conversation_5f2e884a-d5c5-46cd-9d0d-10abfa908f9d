#!/bin/bash
# 一键运行解压脚本

echo "========================================"
echo "603716.SH数据解压工具 - 一键运行"
echo "========================================"

# 检查是否在WSL环境
if ! grep -q Microsoft /proc/version; then
    echo "错误: 请在WSL环境中运行此脚本"
    exit 1
fi

# 检查Python3
if ! command -v python3 &> /dev/null; then
    echo "错误: 未找到Python3，请先运行 ./setup_wsl_environment.sh"
    exit 1
fi

# 检查7z
if ! command -v 7z &> /dev/null; then
    echo "错误: 未找到7z工具，请先运行 ./setup_wsl_environment.sh"
    exit 1
fi

echo "✓ 环境检查通过"
echo ""

# 询问用户要执行的操作
echo "请选择操作:"
echo "1. 测试环境"
echo "2. 开始解压"
echo "3. 测试环境 + 开始解压"
echo ""
read -p "请输入选择 (1-3): " choice

case $choice in
    1)
        echo "运行环境测试..."
        python3 test_wsl_setup.py
        ;;
    2)
        echo "开始解压..."
        python3 extract_603716_data.py
        ;;
    3)
        echo "先运行环境测试..."
        python3 test_wsl_setup.py
        echo ""
        echo "按Enter键继续解压，或Ctrl+C取消..."
        read
        echo "开始解压..."
        python3 extract_603716_data.py
        ;;
    *)
        echo "无效选择，退出"
        exit 1
        ;;
esac

echo ""
echo "操作完成！"
