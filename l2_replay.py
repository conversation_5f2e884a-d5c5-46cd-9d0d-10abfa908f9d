import sys
import csv

from PyQt5.QtCore import QTimer
from PyQt5.QtGui import QPalette, QColor, QIntValidator
from PyQt5.QtWidgets import QApplication, QWidget, QVBoxLayout, QHBoxLayout, QLabel, QPushButton, QFileDialog, QFrame, QLineEdit
import chardet


class Level2Replayer(QWidget):
    def __init__(self):
        super().__init__()

        self.init_ui()
        self.csv_data = []
        self.current_tick = 0
        self.play_speed_multiplier = 1

        self.auto_play_timer = QTimer()
        self.auto_play_timer.timeout.connect(self.play_ticks)

        self.auto_play_button = None

    def init_ui(self):
        self.setWindowTitle("Level 2 Replayer")

        layout = QVBoxLayout()

        self.current_time_label = QLabel("当前时间：")
        layout.addWidget(self.current_time_label)

        self.bid_labels = [QLabel("Ask" + str(10 - i)) for i in (range(10))]
        self.ask_labels = [QLabel("Bid" + str(i + 1)) for i in range(10)]

        self.bid_volume_labels = [QLabel("BidVolume" + str(i + 1)) for i in range(10)]
        self.ask_volume_labels = [QLabel("AskVolume" + str(i + 1)) for i in range(10)]

        bid_ask_layout = QVBoxLayout()
        bid_layout = QVBoxLayout()
        ask_layout = QVBoxLayout()

        for i in range(10):
            bid_row = QHBoxLayout()
            bid_row.addWidget(self.bid_labels[i])
            bid_row.addWidget(self.bid_volume_labels[i])
            bid_layout.addLayout(bid_row)

        for i in range(10):
            ask_row = QHBoxLayout()
            ask_row.addWidget(self.ask_labels[i])
            ask_row.addWidget(self.ask_volume_labels[i])
            ask_layout.addLayout(ask_row)

        bid_ask_layout.addLayout(bid_layout)

        # 添加分割线
        separator = QFrame()
        separator.setFrameShape(QFrame.HLine)
        separator.setFrameShadow(QFrame.Sunken)
        separator_palette = separator.palette()
        separator_palette.setColor(QPalette.Foreground, QColor(255, 0, 0))
        separator.setPalette(separator_palette)
        bid_ask_layout.addWidget(separator)

        bid_ask_layout.addLayout(ask_layout)

        layout.addLayout(bid_ask_layout)

        button_vlayout = QVBoxLayout()

        button_layout1 = QHBoxLayout()

        load_button = QPushButton("加载csv文件")
        load_button.clicked.connect(self.load_csv)
        button_layout1.addWidget(load_button)

        before_button = QPushButton("前一个Tick")
        before_button.clicked.connect(self.before_tick)
        button_layout1.addWidget(before_button)

        next_button = QPushButton("后一个Tick")
        next_button.clicked.connect(self.next_tick)
        button_layout1.addWidget(next_button)

        button_vlayout.addLayout(button_layout1)

        button_layout2 = QHBoxLayout()

        self.speed_input = QLineEdit()
        self.speed_input.setPlaceholderText("输入播放速度")
        button_layout2.addWidget(self.speed_input)

        self.auto_play_button = QPushButton("自动播放")
        self.auto_play_button.clicked.connect(self.toggle_auto_play)
        button_layout2.addWidget(self.auto_play_button)

        self.play_ticks_button = QPushButton("跳动")
        self.play_ticks_button.clicked.connect(self.play_ticks)
        button_layout2.addWidget(self.play_ticks_button)

        self.multiplier_input = QLineEdit()
        self.multiplier_input.setValidator(QIntValidator(1, 1000))
        self.multiplier_input.setText("1")
        button_layout2.addWidget(self.multiplier_input)

        button_vlayout.addLayout(button_layout2)

        layout.addLayout(button_vlayout)

        self.setLayout(layout)

        palette = QPalette()
        palette.setColor(QPalette.Background, QColor(0, 0, 0))
        self.setAutoFillBackground(True)
        self.setPalette(palette)

        for label in self.bid_labels + self.ask_labels + self.bid_volume_labels + self.ask_volume_labels:
            label.setStyleSheet("QLabel { color: white;}")
        self.current_time_label.setStyleSheet("QLabel { color: white; }")



    # def toggle_auto_play(self):
    #     if self.auto_play_timer.isActive():
    #         self.auto_play_timer.stop()
    #     #    self.auto_play_button.setText("自动播放")
    #     else:
    #         self.auto_play_timer.start(1000)
    #         #self.auto_play_button.setText("暂停")
    def toggle_auto_play(self):
        if self.auto_play_timer.isActive():
            self.auto_play_timer.stop()
            #self.auto_play_button.setText("自动播放")
        else:
            try:
                self.play_speed_multiplier = float(self.speed_input.text()) if self.speed_input.text() else 1
                interval = int(1000 / self.play_speed_multiplier)
                self.auto_play_timer.start(interval)
                #self.auto_play_button.setText("暂停")
            except ValueError:
                self.speed_input.setText("请输入有效的速度倍数")

    def load_csv(self):
        file_path, _ = QFileDialog.getOpenFileName(self, "Open CSV File", "", "CSV Files (*.csv)")
        if file_path:
            self.csv_data.clear()

            # 检测文件编码
            with open(file_path, 'rb') as file:
                raw_data = file.read()
                encoding = chardet.detect(raw_data)['encoding']

            # 使用检测到的编码打开文件
            with open(file_path, mode='r', encoding=encoding) as file:
                reader = csv.reader(file)
                self.csv_data = list(reader)

            self.current_tick = 0
            self.update_display()

    def before_tick(self):
        if self.current_tick <= 0:
            return
        self.current_tick -= 1
        self.update_display()

    def next_tick(self):
        self.current_tick += 1
        if self.current_tick >= len(self.csv_data):
            self.current_tick = len(self.csv_data) - 1
        self.update_display()

    def play_ticks(self):
        self.play_speed_multiplier = int(self.multiplier_input.text())
        for _ in range(self.play_speed_multiplier):
            self.next_tick()

    def update_display(self):
        if not self.csv_data:
            return

        tick_data = self.csv_data[self.current_tick]

        if len(tick_data) < 48:  # 确保 tick_data 的长度足够
            return

        for i, label in enumerate(self.ask_labels):
            print(i, label)
            label.setText("Bid{}: {}".format(i + 1, str(float((tick_data[15 + i]))/ 10000)))

        for i, label in enumerate(self.bid_labels):
            label.setText("Ask{}: {}".format(i + 1, str(float((tick_data[14 - i]))/10000)))

        for i, label in enumerate(self.ask_volume_labels):
            label.setText("{}".format(int(tick_data[35 + i])//100))

        for i, label in enumerate(self.bid_volume_labels):
            label.setText("{}".format(int(tick_data[34 - i])//100))

        # Update current time label
        current_time = tick_data[47]
        self.current_time_label.setText("当前时间：{}".format(current_time))
if __name__ == "__main__":
    app = QApplication(sys.argv)
    level2_replayer = Level2Replayer()
    level2_replayer.show()
    sys.exit(app.exec_())


