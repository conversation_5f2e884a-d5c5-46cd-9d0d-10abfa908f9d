import os
path='C:\\Users\\<USER>\\Desktop\\数据示例\\'


def path_name(startDate, endDate, stockCode):
    '''
    path位csv文件放置文件夹路径
    :param startDate: int 20190614
    :param endDate: int 20190614
    :param stockCode: str 000001.SZ
    :return:大于等于开始日期，小于等于结束日期，并且是所选股票代码的csv数据路径,组成列表
    '''
    # print(startDate, endDate, stockCode)

    list_data = []
    for root,dirs,files in os.walk(path):
        for file in files:
            if stockCode in file:
                '''股票代码在文件名中'''
                if startDate<=int(file.split('_')[0])<=endDate:
                    '''int(file.split('_')[0])
                    从文件名中切割出来日期，变位整数，日期在开始日期和结束日期之间'''
                    # print(os.path.join(root,file))
                    '''根据起始日期和股票代码筛选符合条件的csv文件路径'''
                    list_data.append(os.path.join(root,file))
    return list_data

if __name__=='__main__':
    path_name(20190614,20190615,'000001.SZ')



