"""
简单使用示例：演示如何使用1秒动量分析系统
"""

import pandas as pd
import json

def create_sample_level2_data():
    """
    创建符合要求的Level 2样本数据
    模拟真实的SOLUSDT交易数据
    """
    
    # 基础数据
    base_data = {
        'id': [1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19],
        'symbol': ['solusdt'] * 19,
        'timestamp': [
            '2025-4-27 8:02:03', '2025-4-27 8:02:27', '2025-4-27 8:02:56', 
            '2025-4-27 8:02:57', '2025-4-27 8:02:58', '2025-4-27 8:03:03',
            '2025-4-27 8:03:13', '2025-4-27 8:07:42', '2025-4-27 8:07:43',
            '2025-4-27 8:07:44', '2025-4-27 8:07:46', '2025-4-27 8:07:48',
            '2025-4-27 8:08:00', '2025-4-27 8:08:10', '2025-4-27 8:08:11',
            '2025-4-27 8:08:12', '2025-4-27 8:08:13', '2025-4-27 8:08:14',
            '2025-4-27 8:08:17'
        ],
        'update_id': [7367563376294, 7367563472518, 7367566425185] + [7367566425185] * 16,
        'bids_json': [
            '[[149.14, 1014.48], [149.13, 883.52], [149.12, 708.73], [149.11, 1385.65], [149.1, 850.55], [149.09, 1932.18], [149.08, 1766.05], [149.07, 1644.82], [149.06, 2038.03], [149.05, 1230.08], [149.04, 1058.7], [149.03, 1054.9], [149.02, 1202.16], [149.01, 970.2], [149.0, 736.86], [148.99, 1624.4], [148.98, 4086.57], [148.97, 849.1], [148.96, 2796.04], [148.95, 767.59]]',
            '[[149.15, 1383.59], [149.14, 1018.94], [149.13, 706.59], [149.12, 794.58], [149.11, 1179.99], [149.1, 820.66], [149.09, 2031.5], [149.08, 1624.2], [149.07, 1958.06], [149.06, 1694.34], [149.05, 1069.77], [149.04, 1321.9], [149.03, 1584.69], [149.02, 614.76], [149.01, 970.2], [149.0, 736.86], [148.99, 1635.67], [148.98, 4075.3], [148.97, 849.1], [148.96, 2748.34]]',
            '[[149.25, 72.24], [149.24, 249.99], [149.23, 859.74], [149.22, 772.93], [149.21, 643.82], [149.2, 1178.89], [149.19, 1573.99], [149.18, 2098.08], [149.17, 1415.84], [149.16, 1250.62], [149.15, 914.59], [149.14, 633.84], [149.13, 685.19], [149.12, 491.65], [149.11, 1289.47], [149.1, 741.41], [149.09, 452.11], [149.08, 1079.2], [149.07, 1667.92], [149.06, 1667.43]]'
        ] + ['[[149.25, 72.24], [149.24, 249.99], [149.23, 859.74], [149.22, 772.93], [149.21, 643.82]]'] * 16,
        
        'asks_json': [
            '[[149.15, 52.7], [149.16, 159.86], [149.17, 199.03], [149.18, 374.12], [149.19, 407.73], [149.2, 860.49], [149.21, 1008.14], [149.22, 564.1], [149.23, 424.19], [149.24, 527.56], [149.25, 378.7], [149.26, 606.57], [149.27, 736.75], [149.28, 600.9], [149.29, 862.93], [149.3, 1517.41], [149.31, 609.49], [149.32, 1220.92], [149.33, 1526.5], [149.34, 846.08]]',
            '[[149.16, 224.13], [149.17, 184.48], [149.18, 316.88], [149.19, 381.1], [149.2, 657.34], [149.21, 961.0], [149.22, 836.0], [149.23, 526.76], [149.24, 527.56], [149.25, 309.94], [149.26, 478.07], [149.27, 803.95], [149.28, 628.95], [149.29, 715.15], [149.3, 1072.32], [149.31, 1071.94], [149.32, 1011.1], [149.33, 1850.46], [149.34, 846.08], [149.35, 556.75]]',
            '[[149.26, 811.38], [149.27, 572.6], [149.28, 409.96], [149.29, 610.83], [149.3, 1550.74], [149.31, 976.88], [149.32, 873.94], [149.33, 1101.5], [149.34, 816.7], [149.35, 767.26], [149.36, 925.53], [149.37, 913.78], [149.38, 848.2], [149.39, 744.65], [149.4, 704.48], [149.41, 1243.1], [149.42, 1328.64], [149.43, 647.36], [149.44, 432.53], [149.45, 761.57]]'
        ] + ['[[149.26, 811.38], [149.27, 572.6], [149.28, 409.96], [149.29, 610.83], [149.3, 1550.74]]'] * 16,
        
        'best_bid_price': [149.14, 149.15, 149.25] + [149.25] * 16,
        'best_ask_price': [149.15, 149.16, 149.26] + [149.26] * 16,
        'bid_volume': [28600.61, 28819.04, 19738.95] + [19738.95] * 16,
        'ask_volume': [13484.17, 13959.96, 17041.63] + [17041.63] * 16,
        'spread': [0.01, 0.01, 0.01] + [0.01] * 16,
        'spread_percent': [0.00670510929329444, 0.006704659738512172, 0.006700167504181511] + [0.006700167504181511] * 16,
        'created_at': ['03:16.0', '03:16.0', '03:16.0'] + ['03:16.0'] * 16
    }
    
    return pd.DataFrame(base_data)

def example_1_basic_momentum_calculation():
    """示例1：基本动量计算"""
    print("=" * 60)
    print("示例1：基本1秒动量计算")
    print("=" * 60)
    
    # 导入必要的模块
    from one_second_momentum_processor import OneSecondMomentumProcessor
    
    # 创建样本数据
    df = create_sample_level2_data()
    print(f"✓ 创建样本数据：{len(df)} 条Level 2记录")
    
    # 初始化动量处理器
    processor = OneSecondMomentumProcessor(
        data_df=df,
        momentum_window=1.0,  # 1秒窗口
        price_width_percent=0.001  # 0.1%价格宽度
    )
    print("✓ 动量处理器初始化完成")
    
    # 计算动量信号
    signals = processor.calculate_momentum_signals(momentum_threshold=0.01)
    print("✓ 动量信号计算完成")
    
    # 显示结果
    print(f"\n动量值前5个: {signals['momentum_values'][:5]}")
    print(f"多头信号总数: {sum(signals['long_signals'])}")
    print(f"空头信号总数: {sum(signals['short_signals'])}")
    
    # 获取统计信息
    stats = processor.get_signal_statistics()
    print(f"\n统计信息:")
    print(f"  总tick数: {stats['total_ticks']}")
    print(f"  动量均值: {stats['momentum_mean']:.6f}")
    print(f"  动量标准差: {stats['momentum_std']:.6f}")
    print(f"  信号率: {stats['signal_rate']:.2%}")

def example_2_orderbook_display():
    """示例2：盘口显示"""
    print("\n" + "=" * 60)
    print("示例2：Level 2盘口显示")
    print("=" * 60)
    
    # 导入必要的模块
    from level2_orderbook_display import Level2OrderBookDisplay
    
    # 创建样本数据
    df = create_sample_level2_data()
    
    # 初始化盘口显示器
    display = Level2OrderBookDisplay(df, max_depth=5)
    print("✓ 盘口显示器初始化完成")
    
    # 显示第一个盘口快照
    print("\n第一个盘口快照:")
    display.display_current_orderbook(0)
    
    # 显示第三个盘口快照
    print("\n第三个盘口快照:")
    display.display_current_orderbook(2)

def example_3_complete_analysis():
    """示例3：完整分析流程"""
    print("\n" + "=" * 60)
    print("示例3：完整分析流程")
    print("=" * 60)
    
    # 导入主分析模块
    from main_level2_analysis import Level2AnalysisMain
    
    # 创建样本数据
    df = create_sample_level2_data()
    
    # 配置参数
    config = {
        'momentum_window': 1.0,
        'price_width_percent': 0.001,
        'momentum_threshold': 0.01,
        'max_depth': 5,
        'output_dir': 'example_output',
        'save_plots': False,  # 不保存图表
        'save_results': False  # 不保存结果
    }
    
    # 初始化分析器
    analyzer = Level2AnalysisMain(df, config)
    print("✓ 主分析器初始化完成")
    
    # 运行动量分析
    momentum_signals, momentum_stats = analyzer.run_momentum_analysis()
    print("✓ 动量分析完成")
    
    # 运行盘口分析
    summary_report = analyzer.run_orderbook_analysis()
    print("✓ 盘口分析完成")
    
    print(f"\n分析结果摘要:")
    print(f"  处理数据量: {momentum_stats['total_ticks']} 条")
    print(f"  动量信号数: {momentum_stats['long_signal_count'] + momentum_stats['short_signal_count']} 个")
    print(f"  平均价格: {summary_report['price_mean']:.4f}")
    print(f"  价格波动: {summary_report['price_std']:.4f}")

def example_4_custom_parameters():
    """示例4：自定义参数"""
    print("\n" + "=" * 60)
    print("示例4：自定义参数设置")
    print("=" * 60)
    
    from one_second_momentum_processor import OneSecondMomentumProcessor
    
    # 创建样本数据
    df = create_sample_level2_data()
    
    # 测试不同的参数设置
    configs = [
        {'momentum_window': 0.5, 'price_width_percent': 0.0005, 'momentum_threshold': 0.005, 'name': '高敏感度'},
        {'momentum_window': 1.0, 'price_width_percent': 0.001, 'momentum_threshold': 0.01, 'name': '标准设置'},
        {'momentum_window': 2.0, 'price_width_percent': 0.002, 'momentum_threshold': 0.02, 'name': '低敏感度'}
    ]
    
    for config in configs:
        print(f"\n{config['name']}设置:")
        processor = OneSecondMomentumProcessor(
            df, 
            momentum_window=config['momentum_window'],
            price_width_percent=config['price_width_percent']
        )
        
        signals = processor.calculate_momentum_signals(
            momentum_threshold=config['momentum_threshold']
        )
        
        stats = processor.get_signal_statistics()
        print(f"  信号率: {stats['signal_rate']:.2%}")
        print(f"  多头信号: {stats['long_signal_count']} 个")
        print(f"  空头信号: {stats['short_signal_count']} 个")

def example_5_data_export():
    """示例5：数据导出"""
    print("\n" + "=" * 60)
    print("示例5：结果导出")
    print("=" * 60)
    
    from one_second_momentum_processor import OneSecondMomentumProcessor
    
    # 创建样本数据
    df = create_sample_level2_data()
    
    # 处理数据
    processor = OneSecondMomentumProcessor(df)
    signals = processor.calculate_momentum_signals()
    
    # 导出结果
    output_file = processor.export_results('example_momentum_results.csv')
    print(f"✓ 结果已导出到: {output_file}")
    
    # 读取并显示前几行
    try:
        result_df = pd.read_csv(output_file)
        print(f"\n导出文件前5行:")
        print(result_df.head())
        print(f"\n文件包含 {len(result_df)} 行数据")
    except Exception as e:
        print(f"读取导出文件时出错: {e}")

def main():
    """主函数：运行所有示例"""
    print("🚀 1秒动量分析系统使用示例")
    print("基于Level 2数据的高频动量因子计算")
    
    try:
        # 运行各个示例
        example_1_basic_momentum_calculation()
        example_2_orderbook_display()
        example_3_complete_analysis()
        example_4_custom_parameters()
        example_5_data_export()
        
        print("\n" + "=" * 60)
        print("✅ 所有示例运行完成！")
        print("=" * 60)
        
        print("\n📖 使用说明:")
        print("1. 准备Level 2数据CSV文件")
        print("2. 使用OneSecondMomentumProcessor计算动量")
        print("3. 使用Level2OrderBookDisplay显示盘口")
        print("4. 使用Level2AnalysisMain进行完整分析")
        print("5. 根据需要调整参数和阈值")
        
    except Exception as e:
        print(f"\n❌ 运行示例时出错: {e}")
        print("请检查依赖库是否正确安装：pandas, numpy")

if __name__ == "__main__":
    main()
