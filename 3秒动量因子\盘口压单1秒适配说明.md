# 盘口压单1秒数据适配说明

## 📋 概述

基于原有盘口压单本地运行代码，适配Level 2数据格式，实现1秒级盘口压单分析。新系统不仅保留了原有算法的核心逻辑，还增强了数据处理能力和分析功能。

## 🔄 适配改进

### 原有系统 vs 新系统

| 特性 | 原有系统 | 新系统 |
|------|----------|--------|
| 数据源 | Tick数据文件 | Level 2 JSON数据 |
| 时间精度 | 基于文件时间戳 | 1秒级实时数据 |
| 数据格式 | 固定列格式 | 灵活JSON格式 |
| 档位深度 | 固定10档 | 可配置档位 |
| 分析集成 | 独立分析 | 与动量分析集成 |
| 输出格式 | 简单统计 | 详细CSV + 可视化 |

## 📁 新增文件

### 1. `level2_plate_pressure.py` - 核心压单分析模块
- **Level2PlatePressureAnalyzer类**: 主要分析器
- **数据预处理**: 解析JSON格式买卖盘数据
- **阻力计算**: 计算买卖盘在价格宽度内的阻力
- **信号生成**: 基于阻力比例生成压单信号
- **收益评估**: 计算信号的TWAP收益表现

### 2. `integrated_level2_analyzer.py` - 综合分析器
- **IntegratedLevel2Analyzer类**: 集成动量和压单分析
- **信号组合**: 支持AND、OR、加权组合模式
- **综合评估**: 评估组合信号的表现
- **结果导出**: 导出详细的分析结果

### 3. `run_plate_pressure_analysis.py` - 主程序
- **独立压单分析**: 仅运行盘口压单分析
- **综合分析**: 同时运行动量和压单分析
- **命令行接口**: 支持参数配置和文件输入
- **结果展示**: 详细的分析结果展示

## 🔧 核心算法

### 1. 阻力计算

#### 卖盘阻力（做多阻力）
```python
def _long_resist(self, ask_prices, ask_volumes):
    resist = 0
    base_price = ask_prices[0]  # 最优卖价
    
    for i in range(len(ask_prices)):
        price = ask_prices[i]
        volume = ask_volumes[i]
        
        # 在价格宽度范围内的卖量
        if price < base_price + self.width + self.point and price > self.point:
            resist += volume
    
    return resist
```

#### 买盘阻力（做空阻力）
```python
def _short_resist(self, bid_prices, bid_volumes):
    resist = 0
    base_price = bid_prices[0]  # 最优买价
    
    for i in range(len(bid_prices)):
        price = bid_prices[i]
        volume = bid_volumes[i]
        
        # 在价格宽度范围内的买量
        if price > base_price - self.width - self.point and price > self.point:
            resist += volume
    
    return resist
```

### 2. 压单信号生成

#### 多头压单信号
当买盘阻力相对于卖盘阻力过大时，表明有大量买盘压单，可能推高价格：
```
买盘阻力 >= 卖盘阻力 × ratio_low AND 买盘阻力 <= 卖盘阻力 × ratio_high
```

#### 空头压单信号
当卖盘阻力相对于买盘阻力过大时，表明有大量卖盘压单，可能压低价格：
```
卖盘阻力 >= 买盘阻力 × ratio_low AND 卖盘阻力 <= 买盘阻力 × ratio_high
```

### 3. 信号组合策略

#### AND组合（保守）
```python
integrated_long = [momentum_long[i] and pressure_long[i] for i in range(length)]
```

#### OR组合（激进）
```python
integrated_long = [momentum_long[i] or pressure_long[i] for i in range(length)]
```

#### 加权组合（平衡）
```python
long_score = momentum_long[i] * momentum_weight + pressure_long[i] * pressure_weight
integrated_long = 1 if long_score >= 0.5 else 0
```

## 🚀 使用方法

### 1. 快速开始

#### 仅压单分析
```bash
# 使用样本数据
python run_plate_pressure_analysis.py

# 使用自己的数据
python run_plate_pressure_analysis.py your_level2_data.csv
```

#### 综合分析（动量+压单）
```bash
# 使用样本数据
python run_plate_pressure_analysis.py --mode integrated

# 使用自己的数据
python run_plate_pressure_analysis.py your_data.csv --mode integrated
```

### 2. Python代码调用

#### 独立压单分析
```python
from level2_plate_pressure import Level2PlatePressureAnalyzer
import pandas as pd

# 加载数据
df = pd.read_csv('your_level2_data.csv')

# 创建分析器
analyzer = Level2PlatePressureAnalyzer(df, plate_percent=0.002, max_depth=10)

# 计算阻力
analyzer.calculate_resistance()

# 生成信号
analyzer.generate_plate_signals(ratio_low=2.0, ratio_high=10.0)

# 评估表现
long_profits, short_profits = analyzer.calculate_signal_performance()

# 导出结果
analyzer.export_results('pressure_results.csv')
```

#### 综合分析
```python
from integrated_level2_analyzer import IntegratedLevel2Analyzer

# 配置参数
config = {
    'momentum_window': 1.0,
    'momentum_threshold': 0.01,
    'plate_percent': 0.002,
    'plate_ratio_low': 2.0,
    'plate_ratio_high': 8.0,
    'signal_combination_mode': 'AND'
}

# 创建综合分析器
analyzer = IntegratedLevel2Analyzer(df, config)

# 运行完整分析
results = analyzer.run_full_analysis()

# 导出结果
analyzer.export_comprehensive_results()
```

## 📊 输出结果

### 1. 压单分析CSV文件
- `timestamp`: 时间戳
- `symbol`: 交易品种
- `mid_price`: 中间价
- `long_resist`: 卖盘阻力
- `short_resist`: 买盘阻力
- `long_signal`: 多头压单信号
- `short_signal`: 空头压单信号
- `resist_ratio_*`: 阻力比例

### 2. 综合分析CSV文件
- 包含动量信号和压单信号
- 综合信号结果
- 信号对比分析

### 3. 性能统计
- 信号数量和频率
- 平均收益和胜率
- 最大/最小收益
- 阻力统计信息

## ⚙️ 参数配置

### 核心参数

| 参数 | 默认值 | 说明 |
|------|--------|------|
| `plate_percent` | 0.002 | 价格宽度百分比（0.2%） |
| `max_depth` | 10 | 最大档位深度 |
| `ratio_low` | 2.0 | 压单比例下限 |
| `ratio_high` | 10.0 | 压单比例上限 |
| `twap_duration` | 120 | TWAP计算时长（秒） |
| `signal_duration` | 60 | 信号最小间隔（秒） |

### 调参建议

1. **价格宽度** (`plate_percent`):
   - 高波动品种: 0.003-0.005
   - 低波动品种: 0.001-0.002

2. **压单比例** (`ratio_low`, `ratio_high`):
   - 保守策略: 3.0-8.0
   - 激进策略: 1.5-15.0

3. **时间参数**:
   - 高频策略: twap_duration=60, signal_duration=30
   - 中频策略: twap_duration=120, signal_duration=60

## 🔍 算法优势

### 1. 相比原有系统
- **数据适配性**: 支持多种Level 2数据格式
- **实时性**: 1秒级数据处理
- **灵活性**: 可配置参数和档位深度
- **集成性**: 与动量分析无缝集成

### 2. 相比简单技术指标
- **深度信息**: 利用完整盘口深度
- **压单识别**: 识别大单压盘行为
- **时效性**: 捕捉短期价格压力
- **组合效应**: 与动量信号互补

## 📈 应用场景

1. **高频交易**: 1秒级信号生成
2. **套利策略**: 识别短期价格失衡
3. **风险管理**: 监控大单压盘风险
4. **市场微观结构分析**: 研究订单流影响

## 🎯 后续优化方向

1. **机器学习增强**: 使用ML优化参数
2. **多品种分析**: 支持跨品种压单分析
3. **实时流处理**: 支持实时数据流
4. **可视化增强**: 添加实时盘口可视化
5. **策略回测**: 集成完整的回测框架

## 💡 使用建议

1. **参数调优**: 根据具体品种特性调整参数
2. **数据质量**: 确保Level 2数据完整性
3. **信号验证**: 在历史数据上验证信号效果
4. **风险控制**: 结合其他指标进行风险管理
5. **持续监控**: 定期评估信号表现并调整策略
