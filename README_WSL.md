# WSL环境下的7z解压工具

这是一套专门为WSL Linux环境设计的7z文件解压工具，用于从Windows路径下的7z压缩文件中提取603716.SH数据。

## 文件说明

1. **`setup_wsl_environment.sh`** - 自动环境设置脚本
2. **`test_wsl_setup.py`** - WSL环境测试脚本
3. **`extract_603716_data.py`** - 主解压脚本
4. **`extract_7z_files.py`** - 通用7z解压工具

## 快速开始

### 1. 设置环境
```bash
# 给脚本执行权限
chmod +x setup_wsl_environment.sh

# 运行自动设置脚本
./setup_wsl_environment.sh
```

### 2. 测试环境
```bash
# 测试WSL环境是否配置正确
python3 test_wsl_setup.py
```

### 3. 运行解压
```bash
# 开始解压603716.SH数据
python3 extract_603716_data.py
```

## 详细说明

### 环境要求
- WSL (Windows Subsystem for Linux)
- Ubuntu 或其他Debian系Linux发行版
- Python 3.x
- p7zip-full 包

### 路径配置
- **Windows源路径**: `D:\BaiduNetdiskDownload\202507`
- **WSL路径**: `/mnt/d/BaiduNetdiskDownload/202507`
- **输出路径**: `/mnt/d/BaiduNetdiskDownload/202507/603716_SH_data`

### 输出结构
```
603716_SH_data/
├── 20250701/
│   └── 603716.SH/
│       └── [解压的文件]
├── 20250702/
│   └── 603716.SH/
│       └── [解压的文件]
...
└── 20250731/
    └── 603716.SH/
        └── [解压的文件]
```

## 故障排除

### 1. 7z命令未找到
```bash
sudo apt update
sudo apt install p7zip-full
```

### 2. 无法访问Windows路径
检查WSL是否正确挂载Windows文件系统：
```bash
ls /mnt/d/
ls /mnt/c/
```

如果挂载点不存在：
```bash
sudo mkdir -p /mnt/d
sudo mkdir -p /mnt/c
```

### 3. 权限问题
确保脚本有执行权限：
```bash
chmod +x *.py
chmod +x *.sh
```

### 4. 路径不存在
检查Windows路径是否正确：
- 确认 `D:\BaiduNetdiskDownload\202507` 在Windows中存在
- 确认该目录包含7z文件

## 手动测试步骤

### 1. 测试7z工具
```bash
7z
```

### 2. 测试路径访问
```bash
ls /mnt/d/BaiduNetdiskDownload/202507
```

### 3. 测试7z文件列表
```bash
find /mnt/d/BaiduNetdiskDownload/202507 -name "*.7z"
```

### 4. 测试单个7z文件内容
```bash
7z l "/mnt/d/BaiduNetdiskDownload/202507/文件名.7z"
```

## 脚本功能

### test_wsl_setup.py
- 检查7z工具安装
- 测试Windows路径访问
- 验证7z文件可读性
- 检查目标路径是否存在

### extract_603716_data.py
- 自动路径转换 (Windows -> WSL)
- 批量处理7z文件
- 智能文件分类
- 创建日期目录结构
- 详细的进度反馈

## 注意事项

1. **路径格式**: WSL中使用Unix路径格式，Windows路径会自动转换
2. **文件权限**: 确保对目标目录有写权限
3. **磁盘空间**: 确保有足够空间存储解压文件
4. **中文支持**: 脚本支持中文文件名和路径

## 联系支持

如果遇到问题，请检查：
1. WSL版本和配置
2. Windows文件系统挂载状态
3. 7z工具安装状态
4. 路径权限设置
