#!/bin/bash
# WSL环境自动设置脚本

echo "========================================"
echo "WSL环境自动设置脚本"
echo "========================================"

# 检查是否在WSL环境中
if ! grep -q Microsoft /proc/version; then
    echo "错误: 此脚本需要在WSL环境中运行"
    exit 1
fi

echo "✓ 检测到WSL环境"

# 更新包管理器
echo ""
echo "1. 更新包管理器..."
sudo apt update

# 安装7z工具
echo ""
echo "2. 安装p7zip-full..."
sudo apt install -y p7zip-full

# 验证安装
echo ""
echo "3. 验证7z安装..."
if command -v 7z &> /dev/null; then
    echo "✓ 7z 安装成功"
    7z | head -3
else
    echo "✗ 7z 安装失败"
    exit 1
fi

# 检查Windows文件系统挂载
echo ""
echo "4. 检查Windows文件系统挂载..."

if [ -d "/mnt/d" ]; then
    echo "✓ D盘已挂载到 /mnt/d"
else
    echo "⚠ D盘未挂载，尝试创建挂载点..."
    sudo mkdir -p /mnt/d
fi

if [ -d "/mnt/c" ]; then
    echo "✓ C盘已挂载到 /mnt/c"
else
    echo "⚠ C盘未挂载，尝试创建挂载点..."
    sudo mkdir -p /mnt/c
fi

# 检查目标目录
echo ""
echo "5. 检查目标目录..."
TARGET_DIR="/mnt/d/BaiduNetdiskDownload/202507"

if [ -d "$TARGET_DIR" ]; then
    echo "✓ 目标目录存在: $TARGET_DIR"
    
    # 统计7z文件数量
    ZIP_COUNT=$(find "$TARGET_DIR" -name "*.7z" 2>/dev/null | wc -l)
    echo "✓ 找到 $ZIP_COUNT 个7z文件"
    
    if [ $ZIP_COUNT -gt 0 ]; then
        echo "前几个7z文件:"
        find "$TARGET_DIR" -name "*.7z" 2>/dev/null | head -3 | while read file; do
            echo "  - $(basename "$file")"
        done
    fi
else
    echo "✗ 目标目录不存在: $TARGET_DIR"
    echo "请检查Windows路径是否正确"
fi

# 设置Python环境
echo ""
echo "6. 检查Python环境..."
if command -v python3 &> /dev/null; then
    echo "✓ Python3 已安装: $(python3 --version)"
else
    echo "安装Python3..."
    sudo apt install -y python3 python3-pip
fi

# 创建工作目录
echo ""
echo "7. 创建工作目录..."
WORK_DIR="$HOME/extract_603716"
mkdir -p "$WORK_DIR"
echo "✓ 工作目录: $WORK_DIR"

# 设置权限
echo ""
echo "8. 设置脚本权限..."
chmod +x test_wsl_setup.py
chmod +x extract_603716_data.py

echo ""
echo "========================================"
echo "环境设置完成！"
echo "========================================"
echo ""
echo "下一步操作:"
echo "1. 测试环境: python3 test_wsl_setup.py"
echo "2. 运行解压: python3 extract_603716_data.py"
echo ""
echo "如果遇到问题，请检查:"
echo "- Windows路径是否正确"
echo "- WSL是否可以访问Windows文件系统"
echo "- 7z工具是否正常工作"
