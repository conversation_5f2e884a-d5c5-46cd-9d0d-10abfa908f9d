import sys
import csv
import json
from PyQt5.QtCore import Q<PERSON>imer
from PyQt5.QtGui import QPalette, QColor, QIntValidator
from PyQt5.QtWidgets import QApplication, QWidget, QVBoxLayout, QHBoxLayout, QLabel, QPushButton, QFileDialog, QFrame, QLineEdit
import chardet


class Level2Viewer(QWidget):
    def __init__(self):
        super().__init__()
        self.init_ui()
        self.csv_data = []
        self.current_tick = 0
        self.play_speed_multiplier = 1
        
        self.auto_play_timer = QTimer()
        self.auto_play_timer.timeout.connect(self.play_ticks)

    def init_ui(self):
        self.setWindowTitle("Level 2 Market Data Viewer")
        
        layout = QVBoxLayout()
        
        # 顶部信息显示
        info_layout = QHBoxLayout()
        self.symbol_label = QLabel("Symbol: ")
        self.timestamp_label = QLabel("Time: ")
        self.spread_label = QLabel("Spread: ")
        info_layout.addWidget(self.symbol_label)
        info_layout.addWidget(self.timestamp_label)
        info_layout.addWidget(self.spread_label)
        layout.addLayout(info_layout)
        
        # 买卖盘显示
        self.bid_labels = [QLabel(f"Bid{i+1}: ") for i in range(20)]
        self.ask_labels = [QLabel(f"Ask{i+1}: ") for i in range(20)]
        
        bid_ask_layout = QHBoxLayout()
        
        # 买盘 (Bids)
        bid_layout = QVBoxLayout()
        bid_title = QLabel("买盘 (Bids)")
        bid_title.setStyleSheet("QLabel { color: green; font-weight: bold; }")
        bid_layout.addWidget(bid_title)
        
        for label in self.bid_labels:
            label.setStyleSheet("QLabel { color: green; }")
            bid_layout.addWidget(label)
        
        # 卖盘 (Asks)
        ask_layout = QVBoxLayout()
        ask_title = QLabel("卖盘 (Asks)")
        ask_title.setStyleSheet("QLabel { color: red; font-weight: bold; }")
        ask_layout.addWidget(ask_title)
        
        for label in self.ask_labels:
            label.setStyleSheet("QLabel { color: red; }")
            ask_layout.addWidget(label)
        
        bid_ask_layout.addWidget(QWidget())
        bid_ask_layout.addLayout(bid_layout)
        bid_ask_layout.addWidget(QWidget())
        bid_ask_layout.addLayout(ask_layout)
        bid_ask_layout.addWidget(QWidget())
        
        layout.addLayout(bid_ask_layout)
        
        # 控制按钮
        button_layout1 = QHBoxLayout()
        
        load_button = QPushButton("加载CSV文件")
        load_button.clicked.connect(self.load_csv)
        button_layout1.addWidget(load_button)
        
        prev_button = QPushButton("前一个Tick")
        prev_button.clicked.connect(self.prev_tick)
        button_layout1.addWidget(prev_button)
        
        next_button = QPushButton("后一个Tick")
        next_button.clicked.connect(self.next_tick)
        button_layout1.addWidget(next_button)
        
        layout.addLayout(button_layout1)
        
        button_layout2 = QHBoxLayout()
        
        self.speed_input = QLineEdit()
        self.speed_input.setPlaceholderText("播放速度")
        button_layout2.addWidget(self.speed_input)
        
        self.auto_play_button = QPushButton("自动播放")
        self.auto_play_button.clicked.connect(self.toggle_auto_play)
        button_layout2.addWidget(self.auto_play_button)
        
        layout.addLayout(button_layout2)
        
        self.setLayout(layout)
        
        # 设置样式
        palette = QPalette()
        palette.setColor(QPalette.Background, QColor(0, 0, 0))
        self.setAutoFillBackground(True)
        self.setPalette(palette)
        
        for label in [self.symbol_label, self.timestamp_label, self.spread_label]:
            label.setStyleSheet("QLabel { color: white; }")

    def load_csv(self):
        file_path, _ = QFileDialog.getOpenFileName(self, "Open CSV File", "", "CSV Files (*.csv)")
        if file_path:
            self.csv_data.clear()
            
            with open(file_path, 'rb') as file:
                raw_data = file.read()
                encoding = chardet.detect(raw_data)['encoding']
            
            with open(file_path, mode='r', encoding=encoding) as file:
                reader = csv.DictReader(file)
                self.csv_data = list(reader)
            
            self.current_tick = 0
            self.update_display()

    def prev_tick(self):
        if self.current_tick > 0:
            self.current_tick -= 1
            self.update_display()

    def next_tick(self):
        if self.current_tick < len(self.csv_data) - 1:
            self.current_tick += 1
            self.update_display()

    def play_ticks(self):
        self.next_tick()

    def toggle_auto_play(self):
        if self.auto_play_timer.isActive():
            self.auto_play_timer.stop()
            self.auto_play_button.setText("自动播放")
        else:
            try:
                speed = float(self.speed_input.text()) if self.speed_input.text() else 1
                interval = int(1000 / speed)
                self.auto_play_timer.start(interval)
                self.auto_play_button.setText("暂停")
            except ValueError:
                self.speed_input.setText("请输入有效速度")

    def update_display(self):
        if not self.csv_data:
            return
        
        row = self.csv_data[self.current_tick]
        
        # 更新基本信息
        self.symbol_label.setText(f"Symbol: {row['symbol']}")
        self.timestamp_label.setText(f"Time: {row['timestamp']}")
        self.spread_label.setText(f"Spread: {float(row['spread']):.6f} ({float(row['spread_percent']):.4f}%)")
        
        # 解析买卖盘数据
        try:
            bids = json.loads(row['bids_json'])
            asks = json.loads(row['asks_json'])
            
            # 更新买盘显示 (价格从高到低)
            for i, label in enumerate(self.bid_labels):
                if i < len(bids):
                    price, volume = bids[i]
                    label.setText(f"Bid{i+1}: {price} ({volume})")
                else:
                    label.setText(f"Bid{i+1}: ")
            
            # 更新卖盘显示 (价格从低到高)
            for i, label in enumerate(self.ask_labels):
                if i < len(asks):
                    price, volume = asks[i]
                    label.setText(f"Ask{i+1}: {price} ({volume})")
                else:
                    label.setText(f"Ask{i+1}: ")
                    
        except json.JSONDecodeError:
            print("Error parsing JSON data")


if __name__ == "__main__":
    app = QApplication(sys.argv)
    viewer = Level2Viewer()
    viewer.show()
    sys.exit(app.exec_())