# -*- coding: utf-8 -*-
import singleStockPlateSize as ssp
import pandas as pd

processNumber = 40

def getStockList():

    dataFrame = pd.read_csv("stockList_YU.csv", encoding='gbk')
    stockCodeList = []
    stockNameList = []

    for i in range(0,len(dataFrame)):

        stockCodeList.append(dataFrame.iloc[i,0])
        stockNameList.append(dataFrame.iloc[i,1])

    return stockCodeList,stockNameList

def basketStockTest(startDate,endDate,startTime, endTime,platePercent,
                    ratioLowPlateSize,ratioHighPlateSize,tWapDuration,signalDuration):
    stockCodeList,stockNameList = getStockList()
    ret_list = []

    for i in range(len(stockCodeList)):
        print(stockCodeList[i])
        ret_list.append(ssp.getTotalTest(
        startDate,endDate,stockCodeList[i],startTime, endTime,platePercent,ratioLowPlateSize,ratioHighPlateSize,tWapDuration,signalDuration))



    meanLongList = [g[0] for g in ret_list]
    lengthLongList = [g[1] for g in ret_list]
    meanShortList = [g[2] for g in ret_list]
    lengthShortList = [g[3] for g in ret_list]

    movementLong = 0
    movementShort = 0
    for i in range(0, len(meanLongList)):
        movementLong += meanLongList[i] * lengthLongList[i]
        movementShort += meanShortList[i] * lengthShortList[i]
    print(movementLong / sum(lengthLongList), movementShort / sum(lengthShortList))
    print(sum(lengthLongList), sum(lengthShortList))

    result = pd.DataFrame({"stockCode": stockCodeList, "stockName":stockNameList,
                           "meanLong":meanLongList,"lengthLong":lengthLongList,"meanShort":meanShortList,"lengthShort":lengthShortList},
                          index=None)
    str = 'plateSize.csv'
    result.to_csv(str, encoding='gbk', )

if __name__ == '__main__':

    startTime =93100
    endTime = 145000

    #盘口压单比的下限和上限
    ratioHighPlateSize = 1000000.0
    ratioLowPlateSize = 10.0

    platePercent = 0.003
    tWapDuration = 60
    signalDuration = 60

    startDate = 20190614
    endDate = 20190618

    basketStockTest(startDate, endDate, startTime, endTime, platePercent,
                    ratioLowPlateSize, ratioHighPlateSize, tWapDuration, signalDuration)