#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
WSL环境测试脚本
测试7z工具和路径访问
"""

import os
import subprocess
import sys

def test_7z_installation():
    """测试7z是否安装"""
    print("1. 测试7z安装...")
    try:
        result = subprocess.run(['7z'], capture_output=True, text=True)
        print("   ✓ 7z 已安装")
        return True
    except FileNotFoundError:
        print("   ✗ 7z 未安装")
        print("   请运行: sudo apt update && sudo apt install p7zip-full")
        return False

def test_path_access():
    """测试Windows路径访问"""
    print("\n2. 测试Windows路径访问...")
    
    # 测试常见的Windows挂载点
    mount_points = ['/mnt/d', '/mnt/c']
    
    for mount in mount_points:
        if os.path.exists(mount):
            print(f"   ✓ {mount} 可访问")
            try:
                contents = os.listdir(mount)
                print(f"   ✓ {mount} 包含 {len(contents)} 个项目")
            except PermissionError:
                print(f"   ⚠ {mount} 权限不足")
        else:
            print(f"   ✗ {mount} 不存在")
    
    # 测试目标路径
    target_path = "/mnt/d/BaiduNetdiskDownload/202507"
    print(f"\n3. 测试目标路径: {target_path}")
    
    if os.path.exists(target_path):
        print("   ✓ 目标路径存在")
        try:
            files = os.listdir(target_path)
            zip_files = [f for f in files if f.lower().endswith('.7z')]
            print(f"   ✓ 找到 {len(zip_files)} 个7z文件")
            
            if zip_files:
                print("   7z文件列表:")
                for i, f in enumerate(zip_files[:3]):
                    print(f"     {i+1}. {f}")
                if len(zip_files) > 3:
                    print(f"     ... 还有 {len(zip_files) - 3} 个文件")
            return True
        except Exception as e:
            print(f"   ✗ 无法访问目录内容: {e}")
            return False
    else:
        print("   ✗ 目标路径不存在")
        print("   请检查Windows路径是否正确")
        return False

def test_7z_extraction():
    """测试7z解压功能"""
    print("\n4. 测试7z解压功能...")
    
    target_path = "/mnt/d/BaiduNetdiskDownload/202507"
    if not os.path.exists(target_path):
        print("   跳过: 目标路径不存在")
        return False
    
    try:
        files = os.listdir(target_path)
        zip_files = [f for f in files if f.lower().endswith('.7z')]
        
        if not zip_files:
            print("   跳过: 没有7z文件")
            return False
        
        # 选择第一个7z文件进行测试
        test_file = os.path.join(target_path, zip_files[0])
        print(f"   测试文件: {zip_files[0]}")
        
        # 列出压缩文件内容
        cmd = ['7z', 'l', test_file]
        result = subprocess.run(cmd, capture_output=True, text=True)
        
        if result.returncode == 0:
            print("   ✓ 可以读取7z文件内容")
            
            # 检查是否包含目标路径
            if "20250701/603716.SH" in result.stdout:
                print("   ✓ 找到目标路径 20250701/603716.SH")
                return True
            else:
                print("   ⚠ 未找到目标路径 20250701/603716.SH")
                print("   压缩文件内容预览:")
                lines = result.stdout.split('\n')
                for line in lines[:10]:
                    if line.strip():
                        print(f"     {line}")
                return False
        else:
            print(f"   ✗ 无法读取7z文件: {result.stderr}")
            return False
            
    except Exception as e:
        print(f"   ✗ 测试出错: {e}")
        return False

def provide_setup_instructions():
    """提供设置说明"""
    print("\n" + "="*50)
    print("WSL环境设置说明:")
    print("="*50)
    
    print("\n1. 安装7z工具:")
    print("   sudo apt update")
    print("   sudo apt install p7zip-full")
    
    print("\n2. 确保WSL可以访问Windows文件系统:")
    print("   - Windows文件系统通常挂载在 /mnt/ 下")
    print("   - D盘路径: /mnt/d/")
    print("   - C盘路径: /mnt/c/")
    
    print("\n3. 检查路径:")
    print("   ls /mnt/d/BaiduNetdiskDownload/202507")
    
    print("\n4. 如果路径不存在，请检查:")
    print("   - Windows路径是否正确")
    print("   - WSL版本是否支持文件系统访问")
    print("   - 是否需要重新挂载")

def main():
    """主函数"""
    print("WSL环境测试工具")
    print("="*30)
    
    all_tests_passed = True
    
    # 运行所有测试
    if not test_7z_installation():
        all_tests_passed = False
    
    if not test_path_access():
        all_tests_passed = False
    
    if not test_7z_extraction():
        all_tests_passed = False
    
    print("\n" + "="*30)
    if all_tests_passed:
        print("✓ 所有测试通过！环境配置正确")
        print("可以运行主解压脚本: python3 extract_603716_data.py")
    else:
        print("✗ 部分测试失败，需要配置环境")
        provide_setup_instructions()

if __name__ == "__main__":
    main()
