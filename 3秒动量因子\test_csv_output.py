"""
测试CSV输出功能
验证main_level2_analysis.py是否正确输出CSV文件
"""

import pandas as pd
import os
import sys
from datetime import datetime

# 添加当前目录到路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def create_test_data():
    """创建测试数据"""
    sample_data = {
        'id': list(range(1, 21)),  # 20条数据
        'symbol': ['solusdt'] * 20,
        'timestamp': [
            '2025-4-27 8:02:03', '2025-4-27 8:02:04', '2025-4-27 8:02:05', 
            '2025-4-27 8:02:06', '2025-4-27 8:02:07', '2025-4-27 8:02:08',
            '2025-4-27 8:02:09', '2025-4-27 8:02:10', '2025-4-27 8:02:11',
            '2025-4-27 8:02:12', '2025-4-27 8:02:13', '2025-4-27 8:02:14',
            '2025-4-27 8:02:15', '2025-4-27 8:02:16', '2025-4-27 8:02:17',
            '2025-4-27 8:02:18', '2025-4-27 8:02:19', '2025-4-27 8:02:20',
            '2025-4-27 8:02:21', '2025-4-27 8:02:22'
        ],
        'update_id': [7367563376294 + i for i in range(20)],
        'bids_json': [
            '[[149.14, 1014.48], [149.13, 883.52], [149.12, 708.73], [149.11, 1385.65], [149.1, 850.55]]',
            '[[149.15, 1383.59], [149.14, 1018.94], [149.13, 706.59], [149.12, 794.58], [149.11, 1179.99]]',
            '[[149.16, 1200.00], [149.15, 950.00], [149.14, 800.00], [149.13, 750.00], [149.12, 700.00]]',
            '[[149.17, 1100.00], [149.16, 900.00], [149.15, 750.00], [149.14, 650.00], [149.13, 600.00]]',
            '[[149.18, 1050.00], [149.17, 850.00], [149.16, 700.00], [149.15, 600.00], [149.14, 550.00]]'
        ] + ['[[149.20, 1000.00], [149.19, 800.00], [149.18, 650.00], [149.17, 550.00], [149.16, 500.00]]'] * 15,
        
        'asks_json': [
            '[[149.15, 52.7], [149.16, 159.86], [149.17, 199.03], [149.18, 374.12], [149.19, 407.73]]',
            '[[149.16, 224.13], [149.17, 184.48], [149.18, 316.88], [149.19, 381.1], [149.2, 657.34]]',
            '[[149.17, 200.00], [149.18, 180.00], [149.19, 300.00], [149.20, 350.00], [149.21, 400.00]]',
            '[[149.18, 190.00], [149.19, 170.00], [149.20, 290.00], [149.21, 340.00], [149.22, 390.00]]',
            '[[149.19, 180.00], [149.20, 160.00], [149.21, 280.00], [149.22, 330.00], [149.23, 380.00]]'
        ] + ['[[149.21, 170.00], [149.22, 150.00], [149.23, 270.00], [149.24, 320.00], [149.25, 370.00]]'] * 15,
        
        'best_bid_price': [149.14, 149.15, 149.16, 149.17, 149.18] + [149.20] * 15,
        'best_ask_price': [149.15, 149.16, 149.17, 149.18, 149.19] + [149.21] * 15,
        'bid_volume': [4842.28, 4959.71, 4600.00, 4350.00, 4200.00] + [3950.00] * 15,
        'ask_volume': [1393.72, 1763.85, 1430.00, 1370.00, 1310.00] + [1280.00] * 15,
        'spread': [0.01] * 20,
        'spread_percent': [0.0067] * 20,
        'created_at': ['03:16.0'] * 20
    }
    
    return pd.DataFrame(sample_data)

def test_csv_output():
    """测试CSV输出功能"""
    print("=" * 60)
    print("测试CSV输出功能")
    print("=" * 60)
    
    try:
        # 导入主分析模块
        from main_level2_analysis import Level2AnalysisMain
        
        # 创建测试数据
        df = create_test_data()
        print(f"✓ 创建测试数据: {len(df)} 条记录")
        
        # 配置参数
        config = {
            'momentum_window': 1.0,
            'price_width_percent': 0.001,
            'momentum_threshold': 0.01,
            'max_depth': 5,
            'output_dir': 'test_csv_output',
            'save_plots': False,  # 不生成图表
            'save_results': True,  # 强制保存结果
            'force_save': True
        }
        
        # 初始化分析器
        analyzer = Level2AnalysisMain(df, config)
        print("✓ 分析器初始化完成")
        
        # 运行完整分析
        results, saved_files = analyzer.run_full_analysis()
        print("✓ 分析完成")
        
        # 验证输出文件
        print(f"\n📁 验证输出文件 (共 {len(saved_files)} 个):")
        
        for i, file_path in enumerate(saved_files, 1):
            if os.path.exists(file_path):
                file_size = os.path.getsize(file_path)
                print(f"  {i}. ✅ {os.path.basename(file_path)} ({file_size} bytes)")
                
                # 如果是CSV文件，读取并显示前几行
                if file_path.endswith('.csv'):
                    try:
                        csv_df = pd.read_csv(file_path)
                        print(f"     📊 CSV包含 {len(csv_df)} 行, {len(csv_df.columns)} 列")
                        print(f"     📋 列名: {list(csv_df.columns)}")
                        if len(csv_df) > 0:
                            print(f"     📝 前3行数据:")
                            print(csv_df.head(3).to_string(index=False))
                        print()
                    except Exception as e:
                        print(f"     ❌ 读取CSV文件出错: {e}")
                        
            else:
                print(f"  {i}. ❌ {os.path.basename(file_path)} (文件不存在)")
        
        # 显示统计信息
        if 'momentum_stats' in results:
            print("\n📈 动量统计:")
            stats = results['momentum_stats']
            print(f"  总tick数: {stats['total_ticks']}")
            print(f"  多头信号: {stats['long_signal_count']}")
            print(f"  空头信号: {stats['short_signal_count']}")
            print(f"  信号率: {stats['signal_rate']:.2%}")
        
        return True, saved_files
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False, []

def verify_csv_content(file_path):
    """验证CSV文件内容"""
    if not os.path.exists(file_path):
        print(f"❌ 文件不存在: {file_path}")
        return False
    
    try:
        df = pd.read_csv(file_path)
        print(f"\n📊 验证文件: {os.path.basename(file_path)}")
        print(f"  行数: {len(df)}")
        print(f"  列数: {len(df.columns)}")
        print(f"  列名: {list(df.columns)}")
        
        # 检查数据类型
        print(f"  数据类型:")
        for col in df.columns:
            print(f"    {col}: {df[col].dtype}")
        
        # 检查是否有空值
        null_counts = df.isnull().sum()
        if null_counts.sum() > 0:
            print(f"  空值统计:")
            for col, count in null_counts.items():
                if count > 0:
                    print(f"    {col}: {count}")
        else:
            print(f"  ✅ 无空值")
        
        return True
        
    except Exception as e:
        print(f"❌ 验证文件时出错: {e}")
        return False

def main():
    """主函数"""
    print("🚀 开始测试CSV输出功能...")
    
    # 运行测试
    success, saved_files = test_csv_output()
    
    if success and saved_files:
        print(f"\n🔍 详细验证CSV文件...")
        
        # 验证每个CSV文件
        csv_files = [f for f in saved_files if f.endswith('.csv')]
        for csv_file in csv_files:
            verify_csv_content(csv_file)
        
        print(f"\n✅ 测试完成！")
        print(f"📁 输出目录: test_csv_output/")
        print(f"📄 生成文件: {len(saved_files)} 个")
        print(f"📊 CSV文件: {len(csv_files)} 个")
        
        # 提供使用建议
        print(f"\n💡 使用建议:")
        print(f"1. 查看 momentum_results_*.csv 获取动量信号")
        print(f"2. 查看 detailed_analysis_*.csv 获取详细分析")
        print(f"3. 查看 summary_stats_*.csv 获取统计汇总")
        print(f"4. 查看 analysis_summary_*.txt 获取文本报告")
        
    else:
        print(f"\n❌ 测试失败！请检查错误信息。")
    
    return success, saved_files

if __name__ == "__main__":
    success, files = main()
