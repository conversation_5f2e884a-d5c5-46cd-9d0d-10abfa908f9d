# -*- coding: utf-8 -*-
"""
7z压缩文件批量解压脚本
从D:\BaiduNetdiskDownload\202507下的每个7z文件中解压指定的目录
"""

import os
import subprocess
import shutil
from pathlib import Path
import logging

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('extract_log.txt', encoding='utf-8'),
        logging.StreamHandler()
    ]
)

def check_7z_installed():
    """检查7z是否已安装"""
    try:
        result = subprocess.run(['7z'], capture_output=True, text=True)
        return True
    except FileNotFoundError:
        logging.error("7z未安装或未添加到PATH环境变量中")
        logging.error("请安装7-Zip并确保7z.exe在PATH中")
        return False

def extract_specific_path_from_7z(zip_file_path, extract_to_dir, target_path="20250701/603716.SH"):
    """
    从7z文件中解压指定路径
    
    Args:
        zip_file_path: 7z文件路径
        extract_to_dir: 解压目标目录
        target_path: 要解压的目标路径
    """
    try:
        # 确保目标目录存在
        os.makedirs(extract_to_dir, exist_ok=True)
        
        # 构建7z解压命令
        cmd = [
            '7z', 'x',  # x表示解压并保持目录结构
            zip_file_path,
            f"{target_path}/*",  # 解压指定路径下的所有文件
            f'-o{extract_to_dir}',  # 输出目录
            '-y'  # 自动确认所有询问
        ]
        
        logging.info(f"正在解压: {zip_file_path}")
        logging.info(f"目标路径: {target_path}")
        logging.info(f"解压到: {extract_to_dir}")
        
        # 执行解压命令
        result = subprocess.run(cmd, capture_output=True, text=True, encoding='utf-8')
        
        if result.returncode == 0:
            logging.info(f"成功解压: {zip_file_path}")
            return True
        else:
            logging.error(f"解压失败: {zip_file_path}")
            logging.error(f"错误信息: {result.stderr}")
            return False
            
    except Exception as e:
        logging.error(f"解压过程中发生错误: {zip_file_path}, 错误: {str(e)}")
        return False

def create_date_directories(base_dir, start_date="20250701", end_date="20250731"):
    """
    创建日期目录 20250701-20250731
    
    Args:
        base_dir: 基础目录
        start_date: 开始日期
        end_date: 结束日期
    """
    from datetime import datetime, timedelta
    
    start = datetime.strptime(start_date, "%Y%m%d")
    end = datetime.strptime(end_date, "%Y%m%d")
    
    current = start
    created_dirs = []
    
    while current <= end:
        date_str = current.strftime("%Y%m%d")
        date_dir = os.path.join(base_dir, date_str)
        
        if not os.path.exists(date_dir):
            os.makedirs(date_dir, exist_ok=True)
            created_dirs.append(date_dir)
            logging.info(f"创建目录: {date_dir}")
        
        current += timedelta(days=1)
    
    return created_dirs

def process_7z_files(source_dir="D:\\BaiduNetdiskDownload\\202507", 
                    output_base_dir="D:\\BaiduNetdiskDownload\\202507\\extracted",
                    target_path="20250701/603716.SH"):
    """
    处理所有7z文件
    
    Args:
        source_dir: 源目录，包含7z文件
        output_base_dir: 输出基础目录
        target_path: 要解压的目标路径
    """
    
    # 检查7z是否可用
    if not check_7z_installed():
        return False
    
    # 检查源目录是否存在
    if not os.path.exists(source_dir):
        logging.error(f"源目录不存在: {source_dir}")
        return False
    
    # 创建输出目录
    os.makedirs(output_base_dir, exist_ok=True)
    
    # 创建日期目录
    logging.info("创建日期目录...")
    create_date_directories(output_base_dir)
    
    # 查找所有7z文件
    zip_files = []
    for file in os.listdir(source_dir):
        if file.lower().endswith('.7z'):
            zip_files.append(os.path.join(source_dir, file))
    
    if not zip_files:
        logging.warning(f"在 {source_dir} 中未找到7z文件")
        return False
    
    logging.info(f"找到 {len(zip_files)} 个7z文件")
    
    # 处理每个7z文件
    success_count = 0
    for zip_file in zip_files:
        logging.info(f"\n处理文件: {os.path.basename(zip_file)}")
        
        # 为每个7z文件创建单独的解压目录
        file_name = os.path.splitext(os.path.basename(zip_file))[0]
        extract_dir = os.path.join(output_base_dir, file_name)
        
        if extract_specific_path_from_7z(zip_file, extract_dir, target_path):
            success_count += 1
            
            # 检查解压结果
            extracted_path = os.path.join(extract_dir, target_path)
            if os.path.exists(extracted_path):
                file_count = len([f for f in os.listdir(extracted_path) 
                                if os.path.isfile(os.path.join(extracted_path, f))])
                logging.info(f"解压成功，包含 {file_count} 个文件")
            else:
                logging.warning(f"目标路径不存在于压缩文件中: {target_path}")
    
    logging.info(f"\n解压完成！成功处理 {success_count}/{len(zip_files)} 个文件")
    return success_count > 0

def main():
    """主函数"""
    print("7z压缩文件批量解压工具")
    print("=" * 50)
    
    # 配置参数
    source_dir = "D:\\BaiduNetdiskDownload\\202507"
    output_dir = "D:\\BaiduNetdiskDownload\\202507\\extracted"
    target_path = "20250701/603716.SH"
    
    print(f"源目录: {source_dir}")
    print(f"输出目录: {output_dir}")
    print(f"目标路径: {target_path}")
    print("-" * 50)
    
    # 确认是否继续
    confirm = input("是否继续执行？(y/n): ").lower().strip()
    if confirm != 'y':
        print("操作已取消")
        return
    
    # 执行解压
    success = process_7z_files(source_dir, output_dir, target_path)
    
    if success:
        print("\n解压完成！请查看日志文件 extract_log.txt 获取详细信息")
    else:
        print("\n解压过程中遇到问题，请查看日志文件")

if __name__ == "__main__":
    main()
