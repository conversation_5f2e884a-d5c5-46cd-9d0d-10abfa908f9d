"""
Level 2数据综合分析器
集成1秒动量和盘口压单分析

功能：
1. 1秒动量分析
2. 盘口压单分析
3. 综合信号生成
4. 信号表现评估
"""

import pandas as pd
import numpy as np
from datetime import datetime
import warnings
warnings.filterwarnings('ignore')

from one_second_momentum_processor import OneSecondMomentumProcessor
from level2_plate_pressure import Level2PlatePressureAnalyzer

class IntegratedLevel2Analyzer:
    
    def __init__(self, data_df, config=None):
        """
        初始化综合分析器
        
        Args:
            data_df: Level 2数据DataFrame
            config: 配置参数字典
        """
        self.data_df = data_df.copy()
        self.config = config or self._default_config()
        
        # 初始化子分析器
        self.momentum_analyzer = OneSecondMomentumProcessor(
            data_df, 
            momentum_window=self.config['momentum_window'],
            price_width_percent=self.config['momentum_price_width']
        )
        
        self.pressure_analyzer = Level2PlatePressureAnalyzer(
            data_df,
            plate_percent=self.config['plate_percent'],
            max_depth=self.config['max_depth']
        )
        
        # 分析结果
        self.momentum_signals = None
        self.pressure_results = None
        self.integrated_signals = None
        
    def _default_config(self):
        """默认配置"""
        return {
            # 动量分析配置
            'momentum_window': 1.0,
            'momentum_price_width': 0.001,
            'momentum_threshold': 0.01,
            
            # 压单分析配置
            'plate_percent': 0.002,
            'max_depth': 10,
            'plate_ratio_low': 2.0,
            'plate_ratio_high': 10.0,
            
            # 信号评估配置
            'twap_duration': 120,
            'signal_duration': 60,
            
            # 综合信号配置
            'signal_combination_mode': 'AND',  # 'AND', 'OR', 'WEIGHTED'
            'momentum_weight': 0.6,
            'pressure_weight': 0.4
        }
    
    def run_momentum_analysis(self):
        """运行动量分析"""
        print("🔄 运行1秒动量分析...")
        
        self.momentum_signals = self.momentum_analyzer.calculate_momentum_signals(
            momentum_threshold=self.config['momentum_threshold']
        )
        
        momentum_stats = self.momentum_analyzer.get_signal_statistics()
        print(f"✓ 动量分析完成，生成 {momentum_stats['long_signal_count'] + momentum_stats['short_signal_count']} 个信号")
        
        return self.momentum_signals
    
    def run_pressure_analysis(self):
        """运行压单分析"""
        print("🔄 运行盘口压单分析...")
        
        # 计算阻力
        self.pressure_analyzer.calculate_resistance()
        
        # 生成压单信号
        self.pressure_analyzer.generate_plate_signals(
            ratio_low=self.config['plate_ratio_low'],
            ratio_high=self.config['plate_ratio_high']
        )
        
        self.pressure_results = self.pressure_analyzer.get_analysis_results()
        print(f"✓ 压单分析完成，生成 {self.pressure_results['signal_stats']['total_signals']} 个信号")
        
        return self.pressure_results
    
    def generate_integrated_signals(self):
        """生成综合信号"""
        print("🔄 生成综合信号...")
        
        if self.momentum_signals is None or self.pressure_results is None:
            print("请先运行动量分析和压单分析")
            return None
        
        momentum_long = self.momentum_signals['long_signals']
        momentum_short = self.momentum_signals['short_signals']
        pressure_long = self.pressure_analyzer.long_plate_signals
        pressure_short = self.pressure_analyzer.short_plate_signals
        
        # 确保信号长度一致
        min_length = min(len(momentum_long), len(pressure_long))
        momentum_long = momentum_long[:min_length]
        momentum_short = momentum_short[:min_length]
        pressure_long = pressure_long[:min_length]
        pressure_short = pressure_short[:min_length]
        
        # 根据组合模式生成综合信号
        if self.config['signal_combination_mode'] == 'AND':
            integrated_long = [m and p for m, p in zip(momentum_long, pressure_long)]
            integrated_short = [m and p for m, p in zip(momentum_short, pressure_short)]
            
        elif self.config['signal_combination_mode'] == 'OR':
            integrated_long = [m or p for m, p in zip(momentum_long, pressure_long)]
            integrated_short = [m or p for m, p in zip(momentum_short, pressure_short)]
            
        elif self.config['signal_combination_mode'] == 'WEIGHTED':
            # 加权组合
            momentum_weight = self.config['momentum_weight']
            pressure_weight = self.config['pressure_weight']
            
            integrated_long = []
            integrated_short = []
            
            for i in range(min_length):
                # 计算加权分数
                long_score = momentum_long[i] * momentum_weight + pressure_long[i] * pressure_weight
                short_score = momentum_short[i] * momentum_weight + pressure_short[i] * pressure_weight
                
                # 阈值判断（可以调整）
                integrated_long.append(1 if long_score >= 0.5 else 0)
                integrated_short.append(1 if short_score >= 0.5 else 0)
        
        else:
            raise ValueError(f"不支持的信号组合模式: {self.config['signal_combination_mode']}")
        
        self.integrated_signals = {
            'long_signals': integrated_long,
            'short_signals': integrated_short,
            'timestamps': self.momentum_signals['timestamps'][:min_length],
            'symbols': self.momentum_signals['symbols'][:min_length],
            'mid_prices': self.momentum_signals['mid_prices'][:min_length]
        }
        
        long_count = sum(integrated_long)
        short_count = sum(integrated_short)
        total_count = long_count + short_count
        
        print(f"✓ 综合信号生成完成:")
        print(f"  多头信号: {long_count} 个")
        print(f"  空头信号: {short_count} 个")
        print(f"  总信号: {total_count} 个")
        print(f"  信号率: {total_count / min_length:.2%}")
        
        return self.integrated_signals
    
    def evaluate_signal_performance(self):
        """评估信号表现"""
        print("🔄 评估信号表现...")
        
        if self.integrated_signals is None:
            print("请先生成综合信号")
            return None
        
        # 计算综合信号的收益表现
        long_profits, short_profits = self.pressure_analyzer.calculate_signal_performance(
            twap_duration=self.config['twap_duration'],
            signal_duration=self.config['signal_duration']
        )
        
        # 计算统计指标
        performance = {}
        
        if long_profits:
            performance['long'] = {
                'count': len(long_profits),
                'mean_profit': np.mean(long_profits),
                'std_profit': np.std(long_profits),
                'win_rate': sum(1 for p in long_profits if p > 0) / len(long_profits),
                'max_profit': max(long_profits),
                'min_profit': min(long_profits)
            }
        else:
            performance['long'] = {'count': 0}
        
        if short_profits:
            performance['short'] = {
                'count': len(short_profits),
                'mean_profit': np.mean(short_profits),
                'std_profit': np.std(short_profits),
                'win_rate': sum(1 for p in short_profits if p > 0) / len(short_profits),
                'max_profit': max(short_profits),
                'min_profit': min(short_profits)
            }
        else:
            performance['short'] = {'count': 0}
        
        print(f"✓ 信号表现评估完成:")
        if performance['long']['count'] > 0:
            print(f"  多头信号: {performance['long']['count']} 个, 平均收益: {performance['long']['mean_profit']:.2f}bp, 胜率: {performance['long']['win_rate']:.1%}")
        if performance['short']['count'] > 0:
            print(f"  空头信号: {performance['short']['count']} 个, 平均收益: {performance['short']['mean_profit']:.2f}bp, 胜率: {performance['short']['win_rate']:.1%}")
        
        return performance
    
    def run_full_analysis(self):
        """运行完整分析流程"""
        print("🚀 开始Level 2数据综合分析...")
        print("=" * 60)
        
        # 1. 动量分析
        momentum_results = self.run_momentum_analysis()
        
        # 2. 压单分析
        pressure_results = self.run_pressure_analysis()
        
        # 3. 生成综合信号
        integrated_signals = self.generate_integrated_signals()
        
        # 4. 评估信号表现
        performance = self.evaluate_signal_performance()
        
        print("=" * 60)
        print("✅ 综合分析完成！")
        
        return {
            'momentum_results': momentum_results,
            'pressure_results': pressure_results,
            'integrated_signals': integrated_signals,
            'performance': performance
        }
    
    def export_comprehensive_results(self, output_dir='integrated_results'):
        """导出综合结果"""
        import os
        
        os.makedirs(output_dir, exist_ok=True)
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        
        saved_files = []
        
        # 1. 导出动量结果
        if self.momentum_signals:
            momentum_file = os.path.join(output_dir, f'momentum_signals_{timestamp}.csv')
            self.momentum_analyzer.export_results(momentum_file)
            saved_files.append(momentum_file)
        
        # 2. 导出压单结果
        if self.pressure_results:
            pressure_file = os.path.join(output_dir, f'pressure_signals_{timestamp}.csv')
            self.pressure_analyzer.export_results(pressure_file)
            saved_files.append(pressure_file)
        
        # 3. 导出综合信号
        if self.integrated_signals:
            integrated_file = os.path.join(output_dir, f'integrated_signals_{timestamp}.csv')
            self._export_integrated_signals(integrated_file)
            saved_files.append(integrated_file)
        
        print(f"📁 结果已导出到 {output_dir}/ 目录")
        print(f"📄 共保存 {len(saved_files)} 个文件")
        
        return saved_files
    
    def _export_integrated_signals(self, filename):
        """导出综合信号到CSV"""
        if not self.integrated_signals:
            return
        
        # 创建综合信号DataFrame
        integrated_df = pd.DataFrame({
            'timestamp': self.integrated_signals['timestamps'],
            'symbol': self.integrated_signals['symbols'],
            'mid_price': self.integrated_signals['mid_prices'],
            'integrated_long_signal': self.integrated_signals['long_signals'],
            'integrated_short_signal': self.integrated_signals['short_signals'],
            'signal_type': ['Long' if l else 'Short' if s else 'None' 
                           for l, s in zip(self.integrated_signals['long_signals'], 
                                         self.integrated_signals['short_signals'])]
        })
        
        # 添加原始信号对比
        if self.momentum_signals and len(self.momentum_signals['long_signals']) >= len(integrated_df):
            integrated_df['momentum_long'] = self.momentum_signals['long_signals'][:len(integrated_df)]
            integrated_df['momentum_short'] = self.momentum_signals['short_signals'][:len(integrated_df)]
        
        if (hasattr(self.pressure_analyzer, 'long_plate_signals') and 
            len(self.pressure_analyzer.long_plate_signals) >= len(integrated_df)):
            integrated_df['pressure_long'] = self.pressure_analyzer.long_plate_signals[:len(integrated_df)]
            integrated_df['pressure_short'] = self.pressure_analyzer.short_plate_signals[:len(integrated_df)]
        
        integrated_df.to_csv(filename, index=False, encoding='utf-8')
        print(f"✓ 综合信号已导出: {filename}")

def create_sample_data():
    """创建样本数据用于测试"""
    # 这里可以复用之前创建的样本数据函数
    from main_level2_analysis import create_sample_data as create_base_sample
    return create_base_sample()

def main():
    """主函数示例"""
    print("🚀 Level 2数据综合分析示例")
    print("=" * 60)
    
    # 创建样本数据
    df = create_sample_data()
    print(f"📊 使用样本数据: {len(df)} 条记录")
    
    # 配置参数
    config = {
        'momentum_window': 1.0,
        'momentum_price_width': 0.001,
        'momentum_threshold': 0.01,
        'plate_percent': 0.002,
        'max_depth': 5,
        'plate_ratio_low': 2.0,
        'plate_ratio_high': 8.0,
        'twap_duration': 60,
        'signal_duration': 30,
        'signal_combination_mode': 'AND',
        'momentum_weight': 0.6,
        'pressure_weight': 0.4
    }
    
    # 创建综合分析器
    analyzer = IntegratedLevel2Analyzer(df, config)
    
    # 运行完整分析
    results = analyzer.run_full_analysis()
    
    # 导出结果
    saved_files = analyzer.export_comprehensive_results()
    
    return analyzer, results, saved_files

if __name__ == "__main__":
    analyzer, results, files = main()
