"""
快速运行脚本：生成CSV输出
专门用于运行Level 2数据分析并输出CSV结果
"""

import pandas as pd
import os
import sys
from datetime import datetime

# 添加当前目录到路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def create_enhanced_sample_data():
    """创建增强的样本数据，更接近真实交易数据"""
    
    # 生成30条数据，模拟30秒的交易
    n_records = 30
    base_price = 149.15
    
    data = {
        'id': list(range(1, n_records + 1)),
        'symbol': ['solusdt'] * n_records,
        'timestamp': [],
        'update_id': [],
        'bids_json': [],
        'asks_json': [],
        'best_bid_price': [],
        'best_ask_price': [],
        'bid_volume': [],
        'ask_volume': [],
        'spread': [],
        'spread_percent': [],
        'created_at': ['08:02.0'] * n_records
    }
    
    # 生成时间序列
    base_time = datetime(2025, 4, 27, 8, 2, 0)
    for i in range(n_records):
        timestamp = base_time.replace(second=i)
        data['timestamp'].append(timestamp.strftime('%Y-%m-%d %H:%M:%S'))
        data['update_id'].append(7367563376294 + i * 1000)
    
    # 生成价格和量数据
    for i in range(n_records):
        # 价格随机波动
        price_change = (i % 5 - 2) * 0.01  # -0.02 到 +0.02 的波动
        current_price = base_price + price_change
        
        bid_price = current_price - 0.01
        ask_price = current_price
        
        # 生成买盘数据
        bids = []
        total_bid_volume = 0
        for j in range(5):  # 5档买盘
            price = round(bid_price - j * 0.01, 2)
            volume = round(1000 + (i * 50) + (j * 100) + (i % 3) * 200, 2)
            bids.append([price, volume])
            total_bid_volume += volume
        
        # 生成卖盘数据
        asks = []
        total_ask_volume = 0
        for j in range(5):  # 5档卖盘
            price = round(ask_price + j * 0.01, 2)
            volume = round(500 + (i * 25) + (j * 50) + (i % 4) * 100, 2)
            asks.append([price, volume])
            total_ask_volume += volume
        
        # 转换为JSON字符串
        data['bids_json'].append(str(bids).replace("'", '"'))
        data['asks_json'].append(str(asks).replace("'", '"'))
        
        data['best_bid_price'].append(bids[0][0])
        data['best_ask_price'].append(asks[0][0])
        data['bid_volume'].append(total_bid_volume)
        data['ask_volume'].append(total_ask_volume)
        data['spread'].append(round(asks[0][0] - bids[0][0], 4))
        data['spread_percent'].append(round((asks[0][0] - bids[0][0]) / current_price * 100, 6))
    
    return pd.DataFrame(data)

def run_analysis_with_csv_output(data_source=None, output_dir='csv_results'):
    """运行分析并输出CSV"""
    
    print("🚀 开始Level 2数据分析...")
    print("=" * 60)
    
    try:
        # 导入分析模块
        from main_level2_analysis import Level2AnalysisMain
        
        # 准备数据
        if data_source is None:
            print("📊 使用增强样本数据...")
            df = create_enhanced_sample_data()
        elif isinstance(data_source, str):
            print(f"📁 加载数据文件: {data_source}")
            df = pd.read_csv(data_source)
        else:
            df = data_source
        
        print(f"✓ 数据准备完成: {len(df)} 条记录")
        
        # 配置参数
        config = {
            'momentum_window': 1.0,
            'price_width_percent': 0.001,
            'momentum_threshold': 0.01,
            'max_depth': 10,
            'output_dir': output_dir,
            'save_plots': False,  # 不生成图表，专注CSV输出
            'save_results': True,
            'force_save': True
        }
        
        # 创建分析器
        analyzer = Level2AnalysisMain(df, config)
        print("✓ 分析器初始化完成")
        
        # 运行分析
        print("\n🔄 正在运行分析...")
        results, saved_files = analyzer.run_full_analysis()
        
        # 显示结果
        print(f"\n📈 分析结果:")
        if 'momentum_stats' in results:
            stats = results['momentum_stats']
            print(f"  📊 总数据量: {stats['total_ticks']} 条")
            print(f"  📈 多头信号: {stats['long_signal_count']} 个")
            print(f"  📉 空头信号: {stats['short_signal_count']} 个")
            print(f"  🎯 信号率: {stats['signal_rate']:.2%}")
            print(f"  📏 动量均值: {stats['momentum_mean']:.6f}")
            print(f"  📐 动量标准差: {stats['momentum_std']:.6f}")
        
        # 显示保存的文件
        print(f"\n📁 输出文件 (共 {len(saved_files)} 个):")
        csv_files = []
        for i, file_path in enumerate(saved_files, 1):
            if os.path.exists(file_path):
                file_size = os.path.getsize(file_path)
                file_name = os.path.basename(file_path)
                print(f"  {i}. {file_name} ({file_size:,} bytes)")
                
                if file_path.endswith('.csv'):
                    csv_files.append(file_path)
                    # 显示CSV文件的基本信息
                    try:
                        csv_df = pd.read_csv(file_path)
                        print(f"     📊 {len(csv_df)} 行 × {len(csv_df.columns)} 列")
                    except:
                        pass
        
        # 详细显示主要CSV文件内容
        print(f"\n📋 主要CSV文件预览:")
        for csv_file in csv_files:
            if 'momentum_results' in csv_file:
                print(f"\n🎯 动量结果文件: {os.path.basename(csv_file)}")
                try:
                    df_momentum = pd.read_csv(csv_file)
                    print(f"   列名: {list(df_momentum.columns)}")
                    if len(df_momentum) > 0:
                        print("   前5行:")
                        print(df_momentum.head().to_string(index=False))
                except Exception as e:
                    print(f"   读取错误: {e}")
            
            elif 'detailed_analysis' in csv_file:
                print(f"\n📊 详细分析文件: {os.path.basename(csv_file)}")
                try:
                    df_detailed = pd.read_csv(csv_file)
                    print(f"   列名: {list(df_detailed.columns)}")
                    if len(df_detailed) > 0:
                        # 显示信号统计
                        long_signals = df_detailed['long_signal'].sum()
                        short_signals = df_detailed['short_signal'].sum()
                        print(f"   信号统计: 多头={long_signals}, 空头={short_signals}")
                except Exception as e:
                    print(f"   读取错误: {e}")
        
        print(f"\n✅ 分析完成！所有结果已保存到: {output_dir}/")
        return True, saved_files, results
        
    except Exception as e:
        print(f"\n❌ 分析失败: {e}")
        import traceback
        traceback.print_exc()
        return False, [], {}

def main():
    """主函数"""
    print("📈 Level 2数据分析 - CSV输出专用版")
    print("=" * 60)
    
    # 检查命令行参数
    if len(sys.argv) > 1:
        data_file = sys.argv[1]
        if os.path.exists(data_file):
            print(f"📁 使用指定数据文件: {data_file}")
            success, files, results = run_analysis_with_csv_output(data_file)
        else:
            print(f"❌ 数据文件不存在: {data_file}")
            print("🔄 改用样本数据...")
            success, files, results = run_analysis_with_csv_output()
    else:
        print("📊 使用内置样本数据...")
        success, files, results = run_analysis_with_csv_output()
    
    if success:
        print(f"\n🎉 成功！")
        print(f"📂 输出目录: csv_results/")
        print(f"📄 生成文件: {len(files)} 个")
        
        # 提供下一步建议
        print(f"\n💡 下一步:")
        print(f"1. 查看 csv_results/ 目录中的CSV文件")
        print(f"2. 使用Excel或其他工具打开CSV文件进行进一步分析")
        print(f"3. momentum_results_*.csv 包含核心动量信号")
        print(f"4. detailed_analysis_*.csv 包含详细分析数据")
        
    else:
        print(f"\n💥 失败！请检查错误信息。")
    
    return success

if __name__ == "__main__":
    # 使用方法:
    # python run_analysis_csv.py                    # 使用样本数据
    # python run_analysis_csv.py your_data.csv      # 使用指定数据文件
    main()
