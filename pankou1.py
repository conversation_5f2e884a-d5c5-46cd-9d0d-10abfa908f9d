# -*- coding: utf-8 -*-


import pandas as pd
import numpy as np


#传入order_df,返回买单元组和卖单元组
def get_buy_sell_tuple_from_order(order_df):
    order_df = order_df[order_df['委托类型'] == 'A']
    order_df = order_df[order_df['委托数量'] != 0]
    order_df = order_df[order_df['委托代码'].isin(['B','S'])]
    order_df = order_df.reset_index(drop=True)
    pankou_dict = {}
    for row in range(0, len(order_df)):
        if order_df.loc[row, '委托价格'] not in pankou_dict.keys():
            pankou_dict[order_df.loc[row, '委托价格']] = [order_df.loc[row, '委托数量'], order_df.loc[row, '委托代码']]
        else:
            pankou_dict[order_df.loc[row, '委托价格']][0] += order_df.loc[row, '委托数量']
            pankou_dict[order_df.loc[row, '委托价格']][1] = order_df.loc[row, '委托代码']
    buy_order = {}
    sell_order = {}

    for key,value in pankou_dict.items():
        if value[1] == 'B':
            buy_order[key] = value[0]
        else:
            sell_order[key] = value[0]
    buy_order = {k : v for k, v in buy_order.items() if v > 0}
    sell_order = {k : v for k, v in sell_order.items() if v > 0}
    buy_tuple = sorted(buy_order.items(),key=lambda buy_order:buy_order[0],reverse=True)
    sell_tuple = sorted(sell_order.items(), key = lambda sell_order: sell_order[0], reverse = False)
    return buy_tuple, sell_tuple

#每一秒结束后返回一个新的order_df
#传入原来的order_df, 每一秒切片的trans_df

# 传入新的order_df 和 对应的成交记录trans_second_df； 根据成交记录得出新的order_df
def get_second_new_order(order_df,trans_second_df):
    #遍历trans_df
    order_df = order_df.copy()
    order_df = order_df.reset_index(drop=True)
    for row in range(0, len(order_df)):
        if order_df.loc[row, '委托类型'] == 'D':
            exange_order = order_df.loc[row, '交易所委托号']
            # cancel_num = order_df.iloc[row, '委托数量']
            cancel_index = order_df[order_df['交易所委托号'] == exange_order].index[0]
            order_df.loc[cancel_index, '委托数量'] = 0

    for row in range(0, len(trans_second_df)):
        #print(trans_second_df.iloc[row, 10])
        sell_order_num = trans_second_df.iloc[row]['叫卖序号']
        buy_order_num = trans_second_df.iloc[row]['叫买序号']
        sell_index = order_df[order_df['交易所委托号'] == sell_order_num].index[0]
        # print(index)
        if (order_df.loc[sell_index, '委托类型'] != 'A'):
            print(order_df.loc[sell_index, '委托类型'])
        order_df.loc[sell_index, '委托数量'] -= trans_second_df.iloc[row]['成交数量']
        buy_index = order_df[order_df['交易所委托号'] == buy_order_num].index[0]
        order_df.loc[buy_index, '委托数量'] -= trans_second_df.iloc[row]['成交数量']
            

                    
    return order_df

#传入三个参数，已经生成的order_df, 1秒钟切片的order_second_df和trans_second_df
def handle_special(order_df, order_second_df, trans_df, start_time):
    if(start_time == 93228):
        print()
    order_second_df = order_second_df.copy()
    b1_order_df = order_second_df[order_second_df[7] == "b'1'"]
    if len(b1_order_df) > 0:  #如果存在b1
        for row in range(0, len(order_second_df)): #遍历order_second_df
            if(order_second_df.iloc[row, 7] == "b'1'"):
                time = order_second_df.iloc[row, 3]
                print(time)
              #  if time == 93023110: #报错是因为截取的一秒钟没有成交的价格
              #      print()
                #在trans_df里找小于这个时间非撤单的最后一列数据的价格
                trans_temp_df = trans_df[(trans_df[10] != "b'C'") & (trans_df[3] < time)]
                last_price = trans_temp_df.iloc[-1,5] 
                order_second_df.iloc[row, 5] = last_price
                if time == 93023110:
                    print()
    
    bu_order_df = order_second_df[order_second_df[7] == "b'U'"]
    if len(bu_order_df) > 0:# 如果存在bu
        for row in range(0, len(order_second_df)):
            if(order_second_df.iloc[row, 7] == "b'U'"):
                # order_second_df获取当前时间
                time = order_second_df.iloc[row, 3]
                temp_order_second_df = order_second_df[(order_second_df[3] < time)] #获取小于这个时间的order片段
                #把这一秒钟之前的order_second_df 和原先生成的order_df拼接
                temp_order_second_df = pd.concat([order_df, temp_order_second_df], axis=0)
                #在trans_second_df中截取这个时间点之前的数据
                temp_trans_second_df = trans_df[(trans_df[3] // 1000 >= start_time) & (trans_df[3] < time)]
                temp_order_df = get_second_new_order(temp_order_second_df, temp_trans_second_df)
                #根据返回的数据获取的买一，卖一
                buy_tuple, sell_tuple = get_buy_sell_tuple_from_order(temp_order_df)
                #获取到这一行的方向
                direction = order_second_df.iloc[row, 8]
                #修改价格
                if (direction == "b'B'"):
                    order_second_df.iloc[row, 5] = buy_tuple[0][0]
                elif (direction == "b'S'"):
                    order_second_df.iloc[row, 5] = sell_tuple[0][0]
                else:
                    print("error in buy or sell")
    return order_second_df
                      
def get_end_time(start_time, cut_peroid):
    print("start_time:" + str(start_time))
    if start_time == 112959900:
        end_time = 130000000
    else:
        end_time = start_time + cut_peroid    
        if (end_time % 100000 >= 60000):
            end_time += 40000
        if (end_time % 10000000 >= 6000000):
            end_time += 4000000
    print("end_time:" + str(end_time))
    return end_time

def calculate_delta(trans_second_df, prev_best_bid):
    """
    计算每个tick相对于上个tick的delta
    delta = 大于上一个tick买一价的买入成交量 - 小于等于上一个tick的买一价格的卖出成交量

    Args:
        trans_second_df: 当前tick的成交数据
        prev_best_bid: 上一个tick的买一价格

    Returns:
        tuple: (买入成交量, 卖出成交量, delta)
    """
    if len(trans_second_df) == 0 or prev_best_bid is None:
        return 0, 0, 0

    buy_volume = 0  # 大于上一个tick买一价的买入成交量
    sell_volume = 0  # 小于等于上一个tick买一价的卖出成交量

    for row in range(len(trans_second_df)):
        # 获取成交价格和成交数量
        try:
            # 尝试使用列名获取成交价格
            if '成交价格' in trans_second_df.columns:
                trans_price = trans_second_df.iloc[row]['成交价格']
            else:
                # 使用索引获取成交价格（第5列）
                trans_price = trans_second_df.iloc[row, 5]

            # 获取成交数量
            trans_volume = trans_second_df.iloc[row]['成交数量']
        except (KeyError, IndexError):
            # 如果获取失败，跳过这条记录
            continue

        if trans_price > prev_best_bid:
            # 成交价格大于上一个tick买一价，认为是买入成交
            buy_volume += trans_volume
        else:
            # 成交价格小于等于上一个tick买一价，认为是卖出成交
            sell_volume += trans_volume

    delta = buy_volume - sell_volume
    print("buy_volume:" + str(buy_volume))
    print("sell_volume:" + str(sell_volume))
    print("delta:" + str(delta))
    print("all_volum:" + str(trans_second_df['成交数量'].sum()))
    return buy_volume, sell_volume, delta



def pankou(trans_file, order_file):
    n = 20

    trans_df = pd.read_csv(trans_file, encoding='gbk')
    order_df = pd.read_csv(order_file, encoding='gbk')
    pankou_list = []


    trans_jihe_df = trans_df[(trans_df['时间'] >= 91500000) & (trans_df['时间'] <= 92500000)]
    order_jihe_df = order_df[(order_df['时间'] >= 91500000) & (order_df['时间'] <= 92500000)]


    order_jihe_df_copy = get_second_new_order(order_jihe_df, trans_jihe_df)
    buy_tuple, sell_tuple = get_buy_sell_tuple_from_order(order_jihe_df_copy)
    #
    pankou_list.append([93000000, buy_tuple[0:n], sell_tuple[0:n]])
    print(pankou_list)
    #
    # 初始化delta相关变量
    prev_best_bid = buy_tuple[0][0] if len(buy_tuple) > 0 else None
    buy_vol, sell_vol, delta = 0, 0, 0  # 初始tick的delta为0

    tmp_list = [93000000] + list(np.array(buy_tuple[0:n])[:, 0]) + list(np.array(sell_tuple[0:n])[:, 0]) + \
            list(np.array(buy_tuple[0:n])[:, 1]) + list(np.array(sell_tuple[0:n])[:, 1]) + [buy_vol, sell_vol, delta]
    pankou_df = pd.DataFrame(tmp_list).T
    #
    start_time = 93000000 #开始时间 9:30:00
    cut_peroid = 100  #截取周期 100ms
    end_time = get_end_time(start_time, cut_peroid)
    #
    #
    while(start_time < 145700000):
        trans_second_df = trans_df[(trans_df['时间']  >= start_time) &
                                   (trans_df['时间'] < end_time)]
        order_second_df = order_df[(order_df['时间']  >= start_time) &
                                   (order_df['时间']  < end_time)]

        # 计算当前tick的delta
        buy_vol, sell_vol, delta = calculate_delta(trans_second_df, prev_best_bid)

        # order_second_df = handle_special(order_jihe_df_copy, order_second_df, trans_df, start_time)
        order_jihe_df_copy = pd.concat([order_jihe_df_copy, order_second_df])
        order_jihe_df_copy = get_second_new_order(order_jihe_df_copy,trans_second_df)
        buy_tuple, sell_tuple = get_buy_sell_tuple_from_order(order_jihe_df_copy)
        print([end_time, buy_tuple[0:n], sell_tuple[0:n]])

        pankou_list.append([end_time, buy_tuple[0:n], sell_tuple[0:n]])

        # 更新prev_best_bid为当前tick的买一价
        prev_best_bid = buy_tuple[0][0] if len(buy_tuple) > 0 else prev_best_bid
    #
        tmp_list = [end_time] + list(np.array(buy_tuple[0:n])[:, 0]) + list(np.array(sell_tuple[0:n])[:, 0]) + \
            list(np.array(buy_tuple[0:n])[:, 1]) + list(np.array(sell_tuple[0:n])[:, 1]) + [buy_vol, sell_vol, delta]
        tmp_df = pd.DataFrame(tmp_list).T
        pankou_df = pd.concat([pankou_df, tmp_df], axis=0)
    #
        if end_time >= 95900000:
            pankou_df.columns = ['time'] + ['buy_price_' + str(i) for i in range(1, n+1)] + ['sell_price_' + str(i) for i in range(1, n+1)] + \
            ['buy_volume_' + str(i) for i in range(1, n+1)] + ['sell_volume_' + str(i) for i in range(1, n+1)] + \
            ['buy_trade_volume', 'sell_trade_volume', 'delta']
            return pankou_df


        start_time = end_time
        end_time = get_end_time(start_time, cut_peroid)
    return pankou_list

if __name__ == '__main__':
    date = '20250703'
    code = '113601.SZ'
    trans_csv_file = rf'D:\BaiduNetdiskDownload\202507\{date}\{code}\逐笔成交.csv'
    order_csv_file = rf'D:\BaiduNetdiskDownload\202507\{date}\{code}\逐笔委托.csv'
   # check_test()
    
    pankou_data = pankou(trans_csv_file, order_csv_file)
    pankou_data.to_csv(rf'D:\BaiduNetdiskDownload\202507\{date}\{code}\market_ms.csv')
    #get_end_time(113000, 1)