import sys
import csv
import pandas as pd
from PyQt5.QtCore import QTimer
from PyQt5.QtGui import QPalette, QColor, QIntValidator
from PyQt5.QtWidgets import QApplication, QWidget, QVBoxLayout, QHBoxLayout, QLabel, QPushButton, QFileDialog, QFrame, QLineEdit
import chardet


class StockLevel2Viewer(QWidget):
    def __init__(self):
        super().__init__()
        self.init_ui()
        self.csv_data = []
        self.current_tick = 0
        self.play_speed_multiplier = 1
        
        self.auto_play_timer = QTimer()
        self.auto_play_timer.timeout.connect(self.play_ticks)

    def init_ui(self):
        self.setWindowTitle("Stock Level 2 Market Data Viewer")
        
        layout = QVBoxLayout()
        
        # 顶部信息显示
        info_layout = QHBoxLayout()
        self.time_label = QLabel("Time: ")
        self.spread_label = QLabel("Spread: ")
        self.tick_label = QLabel("Tick: 0/0")
        info_layout.addWidget(self.time_label)
        info_layout.addWidget(self.spread_label)
        info_layout.addWidget(self.tick_label)
        layout.addLayout(info_layout)
        
        # 买卖盘显示
        self.bid_labels = [QLabel(f"买{i+1}: ") for i in range(20)]
        self.ask_labels = [QLabel(f"卖{i+1}: ") for i in range(20)]
        
        bid_ask_layout = QHBoxLayout()
        
        # 买盘 (Bids)
        bid_layout = QVBoxLayout()
        bid_title = QLabel("买盘 (Bids)")
        bid_title.setStyleSheet("QLabel { color: green; font-weight: bold; }")
        bid_layout.addWidget(bid_title)
        
        for label in self.bid_labels:
            label.setStyleSheet("QLabel { color: green; }")
            bid_layout.addWidget(label)
        
        # 卖盘 (Asks)
        ask_layout = QVBoxLayout()
        ask_title = QLabel("卖盘 (Asks)")
        ask_title.setStyleSheet("QLabel { color: red; font-weight: bold; }")
        ask_layout.addWidget(ask_title)
        
        for label in self.ask_labels:
            label.setStyleSheet("QLabel { color: red; }")
            ask_layout.addWidget(label)
        
        bid_ask_layout.addWidget(QWidget())
        bid_ask_layout.addLayout(bid_layout)
        bid_ask_layout.addWidget(QWidget())
        bid_ask_layout.addLayout(ask_layout)
        bid_ask_layout.addWidget(QWidget())
        
        layout.addLayout(bid_ask_layout)
        
        # 控制按钮
        button_layout1 = QHBoxLayout()
        
        load_button = QPushButton("加载CSV文件")
        load_button.clicked.connect(self.load_csv)
        button_layout1.addWidget(load_button)
        
        prev_button = QPushButton("前一个Tick")
        prev_button.clicked.connect(self.prev_tick)
        button_layout1.addWidget(prev_button)
        
        next_button = QPushButton("后一个Tick")
        next_button.clicked.connect(self.next_tick)
        button_layout1.addWidget(next_button)
        
        layout.addLayout(button_layout1)
        
        button_layout2 = QHBoxLayout()

        self.speed_input = QLineEdit()
        self.speed_input.setPlaceholderText("播放速度")
        button_layout2.addWidget(self.speed_input)

        self.auto_play_button = QPushButton("自动播放")
        self.auto_play_button.clicked.connect(self.toggle_auto_play)
        button_layout2.addWidget(self.auto_play_button)

        layout.addLayout(button_layout2)

        # 时间跳转控件
        time_jump_layout = QHBoxLayout()

        self.time_input = QLineEdit()
        self.time_input.setPlaceholderText("输入时间戳 (例如: 09:30:00)")
        time_jump_layout.addWidget(self.time_input)

        jump_button = QPushButton("跳转到时间")
        jump_button.clicked.connect(self.jump_to_time)
        time_jump_layout.addWidget(jump_button)

        layout.addLayout(time_jump_layout)
        
        self.setLayout(layout)
        
        # 设置样式
        palette = QPalette()
        palette.setColor(QPalette.Background, QColor(0, 0, 0))
        self.setAutoFillBackground(True)
        self.setPalette(palette)
        
        for label in [self.time_label, self.spread_label, self.tick_label]:
            label.setStyleSheet("QLabel { color: white; }")

        # 设置输入框样式
        for input_widget in [self.speed_input, self.time_input]:
            input_widget.setStyleSheet("QLineEdit { color: white; background-color: #333333; border: 1px solid #555555; }")

    def load_csv(self):
        file_path, _ = QFileDialog.getOpenFileName(self, "Open CSV File", "", "CSV Files (*.csv)")
        if file_path:
            self.csv_data.clear()
            
            try:
                # 使用pandas读取CSV，处理编码问题
                df = pd.read_csv(file_path, encoding='utf-8')
                self.csv_data = df.to_dict('records')
            except UnicodeDecodeError:
                # 如果UTF-8失败，尝试检测编码
                with open(file_path, 'rb') as file:
                    raw_data = file.read()
                    encoding = chardet.detect(raw_data)['encoding']
                df = pd.read_csv(file_path, encoding=encoding)
                self.csv_data = df.to_dict('records')
            
            self.current_tick = 0
            self.update_display()
            print(f"加载了 {len(self.csv_data)} 条数据")

    def prev_tick(self):
        if self.current_tick > 0:
            self.current_tick -= 1
            self.update_display()

    def next_tick(self):
        if self.current_tick < len(self.csv_data) - 1:
            self.current_tick += 1
            self.update_display()

    def play_ticks(self):
        self.next_tick()

    def toggle_auto_play(self):
        if self.auto_play_timer.isActive():
            self.auto_play_timer.stop()
            self.auto_play_button.setText("自动播放")
        else:
            try:
                speed = float(self.speed_input.text()) if self.speed_input.text() else 1
                interval = int(1000 / speed)
                self.auto_play_timer.start(interval)
                self.auto_play_button.setText("暂停")
            except ValueError:
                self.speed_input.setText("请输入有效速度")

    def jump_to_time(self):
        """跳转到指定时间戳"""
        if not self.csv_data:
            self.time_input.setText("请先加载CSV文件")
            return

        target_time = self.time_input.text().strip()
        if not target_time:
            self.time_input.setText("请输入时间")
            return

        # 查找最接近的时间戳
        closest_index = -1

        for i, row in enumerate(self.csv_data):
            row_time = str(row.get('time', ''))

            # 支持多种时间格式匹配
            if target_time in row_time:
                closest_index = i
                break

            # 如果是精确匹配时间格式，计算时间差
            try:
                # 提取时间部分进行比较
                if len(target_time) >= 8:  # HH:MM:SS格式
                    if row_time.find(target_time) != -1:
                        closest_index = i
                        break
                elif len(target_time) >= 5:  # HH:MM格式
                    if row_time.find(target_time) != -1:
                        closest_index = i
                        break
            except:
                continue

        if closest_index != -1:
            self.current_tick = closest_index
            self.update_display()
            self.time_input.setText(f"已跳转到第{closest_index + 1}条数据")
        else:
            self.time_input.setText("未找到匹配的时间")

    def update_display(self):
        if not self.csv_data:
            return
        
        row = self.csv_data[self.current_tick]
        
        # 更新基本信息
        self.time_label.setText(f"Time: {row.get('time', 'N/A')}")
        self.tick_label.setText(f"Tick: {self.current_tick + 1}/{len(self.csv_data)}")
        
        # 计算价差
        try:
            best_bid = float(row.get('buy_price_1', 0)) / 10000
            best_ask = float(row.get('sell_price_1', 0)) / 10000
            if best_bid > 0 and best_ask > 0:
                spread = best_ask - best_bid
                spread_percent = (spread / best_ask) * 100 if best_ask > 0 else 0
                self.spread_label.setText(f"Spread: {spread:.4f} ({spread_percent:.4f}%)")
            else:
                self.spread_label.setText("Spread: N/A")
        except (ValueError, TypeError):
            self.spread_label.setText("Spread: N/A")
        
        # 更新买盘显示 (价格从高到低)
        for i, label in enumerate(self.bid_labels):
            try:
                price_key = f'buy_price_{i+1}'
                volume_key = f'buy_volume_{i+1}'
                
                price = row.get(price_key, '')
                volume = row.get(volume_key, '')
                
                if price and volume and str(price) != 'nan' and str(volume) != 'nan':
                    label.setText(f"买{i+1}: {float(price)/10000:.4f} ({int(float(volume))})")
                else:
                    label.setText(f"买{i+1}: ")
            except (ValueError, TypeError):
                label.setText(f"买{i+1}: ")
        
        # 更新卖盘显示 (价格从低到高)
        for i, label in enumerate(self.ask_labels):
            try:
                price_key = f'sell_price_{i+1}'
                volume_key = f'sell_volume_{i+1}'
                
                price = row.get(price_key, '')
                volume = row.get(volume_key, '')
                
                if price and volume and str(price) != 'nan' and str(volume) != 'nan':
                    label.setText(f"卖{i+1}: {float(price)/10000:.4f} ({int(float(volume))})")
                else:
                    label.setText(f"卖{i+1}: ")
            except (ValueError, TypeError):
                label.setText(f"卖{i+1}: ")


if __name__ == "__main__":
    app = QApplication(sys.argv)
    viewer = StockLevel2Viewer()
    viewer.show()
    sys.exit(app.exec_())