"""
盘口压单分析主程序
基于Level 2数据运行盘口压单分析，适配1秒数据

使用方法：
python run_plate_pressure_analysis.py                    # 使用样本数据
python run_plate_pressure_analysis.py your_data.csv      # 使用指定数据文件
"""

import pandas as pd
import numpy as np
import os
import sys
import argparse
from datetime import datetime

# 添加当前目录到路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from level2_plate_pressure import Level2PlatePressureAnalyzer
from integrated_level2_analyzer import IntegratedLevel2Analyzer

def create_enhanced_sample_data():
    """创建增强的样本数据"""
    # 生成更多数据点，模拟真实的盘口压单场景
    n_records = 50
    base_price = 149.15
    
    data = {
        'id': list(range(1, n_records + 1)),
        'symbol': ['solusdt'] * n_records,
        'timestamp': [],
        'update_id': [],
        'bids_json': [],
        'asks_json': [],
        'best_bid_price': [],
        'best_ask_price': [],
        'bid_volume': [],
        'ask_volume': [],
        'spread': [],
        'spread_percent': [],
        'created_at': ['08:02.0'] * n_records
    }
    
    # 生成时间序列
    base_time = datetime(2025, 4, 27, 8, 2, 0)
    for i in range(n_records):
        timestamp = base_time.replace(second=i)
        data['timestamp'].append(timestamp.strftime('%Y-%m-%d %H:%M:%S'))
        data['update_id'].append(7367563376294 + i * 1000)
    
    # 生成价格和量数据，模拟压单场景
    for i in range(n_records):
        # 价格随机波动
        price_change = np.sin(i * 0.2) * 0.02  # 正弦波动
        current_price = base_price + price_change
        
        bid_price = current_price - 0.01
        ask_price = current_price
        
        # 模拟压单场景：某些时刻买盘或卖盘明显增大
        pressure_factor = 1.0
        if i % 10 == 3:  # 每10个tick中第4个模拟买盘压单
            pressure_factor = 3.0
        elif i % 10 == 7:  # 每10个tick中第8个模拟卖盘压单
            pressure_factor = 0.3
        
        # 生成买盘数据
        bids = []
        total_bid_volume = 0
        for j in range(10):  # 10档买盘
            price = round(bid_price - j * 0.01, 2)
            base_volume = 1000 + (i * 20) + (j * 50)
            
            # 应用压单因子
            if pressure_factor > 1:  # 买盘压单
                volume = round(base_volume * pressure_factor * (1 - j * 0.1), 2)
            else:
                volume = round(base_volume, 2)
            
            bids.append([price, volume])
            total_bid_volume += volume
        
        # 生成卖盘数据
        asks = []
        total_ask_volume = 0
        for j in range(10):  # 10档卖盘
            price = round(ask_price + j * 0.01, 2)
            base_volume = 500 + (i * 10) + (j * 25)
            
            # 应用压单因子
            if pressure_factor < 1:  # 卖盘压单
                volume = round(base_volume / pressure_factor * (1 - j * 0.1), 2)
            else:
                volume = round(base_volume, 2)
            
            asks.append([price, volume])
            total_ask_volume += volume
        
        # 转换为JSON字符串
        data['bids_json'].append(str(bids).replace("'", '"'))
        data['asks_json'].append(str(asks).replace("'", '"'))
        
        data['best_bid_price'].append(bids[0][0])
        data['best_ask_price'].append(asks[0][0])
        data['bid_volume'].append(total_bid_volume)
        data['ask_volume'].append(total_ask_volume)
        data['spread'].append(round(asks[0][0] - bids[0][0], 4))
        data['spread_percent'].append(round((asks[0][0] - bids[0][0]) / current_price * 100, 6))
    
    return pd.DataFrame(data)

def run_plate_pressure_only(data_source=None, output_dir='plate_pressure_results'):
    """只运行盘口压单分析"""
    print("🔍 盘口压单分析")
    print("=" * 60)
    
    try:
        # 准备数据
        if data_source is None:
            print("📊 使用增强样本数据...")
            df = create_enhanced_sample_data()
        elif isinstance(data_source, str):
            print(f"📁 加载数据文件: {data_source}")
            df = pd.read_csv(data_source)
        else:
            df = data_source
        
        print(f"✓ 数据准备完成: {len(df)} 条记录")
        
        # 配置参数
        config = {
            'plate_percent': 0.002,  # 0.2%价格宽度
            'max_depth': 10,         # 10档深度
            'ratio_low': 2.0,        # 压单比例下限
            'ratio_high': 10.0,      # 压单比例上限
            'twap_duration': 60,     # TWAP持续时间60秒
            'signal_duration': 30    # 信号间隔30秒
        }
        
        # 创建分析器
        analyzer = Level2PlatePressureAnalyzer(
            df, 
            plate_percent=config['plate_percent'],
            max_depth=config['max_depth']
        )
        
        print("✓ 压单分析器初始化完成")
        
        # 运行分析
        print("\n🔄 计算买卖盘阻力...")
        analyzer.calculate_resistance()
        
        print("🔄 生成压单信号...")
        analyzer.generate_plate_signals(
            ratio_low=config['ratio_low'],
            ratio_high=config['ratio_high']
        )
        
        print("🔄 计算信号表现...")
        long_profits, short_profits = analyzer.calculate_signal_performance(
            twap_duration=config['twap_duration'],
            signal_duration=config['signal_duration']
        )
        
        # 获取分析结果
        results = analyzer.get_analysis_results()
        
        # 显示结果
        print(f"\n📈 分析结果:")
        print(f"  📊 总数据量: {results['total_ticks']} 条")
        print(f"  📏 价格宽度: {results['price_width']:.4f}")
        print(f"  📈 多头压单信号: {results['signal_stats']['long_signals']} 个")
        print(f"  📉 空头压单信号: {results['signal_stats']['short_signals']} 个")
        print(f"  🎯 总信号率: {results['signal_stats']['signal_rate']:.2%}")
        
        # 显示阻力统计
        print(f"\n📊 买卖盘阻力统计:")
        print(f"  卖盘阻力 - 均值: {results['long_resist_stats']['mean']:.2f}, 标准差: {results['long_resist_stats']['std']:.2f}")
        print(f"  买盘阻力 - 均值: {results['short_resist_stats']['mean']:.2f}, 标准差: {results['short_resist_stats']['std']:.2f}")
        
        # 显示信号表现
        if long_profits:
            long_mean = np.mean(long_profits)
            long_win_rate = sum(1 for p in long_profits if p > 0) / len(long_profits)
            print(f"\n📈 多头信号表现:")
            print(f"  信号数量: {len(long_profits)}")
            print(f"  平均收益: {long_mean:.2f} bp")
            print(f"  胜率: {long_win_rate:.1%}")
        
        if short_profits:
            short_mean = np.mean(short_profits)
            short_win_rate = sum(1 for p in short_profits if p > 0) / len(short_profits)
            print(f"\n📉 空头信号表现:")
            print(f"  信号数量: {len(short_profits)}")
            print(f"  平均收益: {short_mean:.2f} bp")
            print(f"  胜率: {short_win_rate:.1%}")
        
        # 导出结果
        os.makedirs(output_dir, exist_ok=True)
        output_file = analyzer.export_results(
            os.path.join(output_dir, f'plate_pressure_analysis_{datetime.now().strftime("%Y%m%d_%H%M%S")}.csv')
        )
        
        print(f"\n✅ 分析完成！结果已保存到: {output_dir}/")
        return True, analyzer, results, (long_profits, short_profits)
        
    except Exception as e:
        print(f"\n❌ 分析失败: {e}")
        import traceback
        traceback.print_exc()
        return False, None, None, None

def run_integrated_analysis(data_source=None, output_dir='integrated_results'):
    """运行综合分析（动量+压单）"""
    print("🔄 综合分析（1秒动量 + 盘口压单）")
    print("=" * 60)
    
    try:
        # 准备数据
        if data_source is None:
            print("📊 使用增强样本数据...")
            df = create_enhanced_sample_data()
        elif isinstance(data_source, str):
            print(f"📁 加载数据文件: {data_source}")
            df = pd.read_csv(data_source)
        else:
            df = data_source
        
        print(f"✓ 数据准备完成: {len(df)} 条记录")
        
        # 配置参数
        config = {
            'momentum_window': 1.0,
            'momentum_price_width': 0.001,
            'momentum_threshold': 0.01,
            'plate_percent': 0.002,
            'max_depth': 10,
            'plate_ratio_low': 2.0,
            'plate_ratio_high': 8.0,
            'twap_duration': 60,
            'signal_duration': 30,
            'signal_combination_mode': 'AND'
        }
        
        # 创建综合分析器
        analyzer = IntegratedLevel2Analyzer(df, config)
        
        # 运行完整分析
        results = analyzer.run_full_analysis()
        
        # 导出结果
        saved_files = analyzer.export_comprehensive_results(output_dir)
        
        print(f"\n✅ 综合分析完成！")
        print(f"📁 结果保存目录: {output_dir}/")
        print(f"📄 保存文件数: {len(saved_files)}")
        
        return True, analyzer, results, saved_files
        
    except Exception as e:
        print(f"\n❌ 综合分析失败: {e}")
        import traceback
        traceback.print_exc()
        return False, None, None, None

def main():
    """主函数"""
    parser = argparse.ArgumentParser(description='Level 2盘口压单分析程序')
    parser.add_argument('data', nargs='?', help='数据文件路径（可选）')
    parser.add_argument('--mode', choices=['pressure', 'integrated'], default='pressure',
                       help='分析模式：pressure=仅压单分析，integrated=综合分析')
    parser.add_argument('--output', default='results', help='输出目录')
    
    args = parser.parse_args()
    
    print("🚀 Level 2数据盘口压单分析系统")
    print("=" * 60)
    
    if args.mode == 'pressure':
        success, analyzer, results, profits = run_plate_pressure_only(args.data, args.output)
    else:
        success, analyzer, results, files = run_integrated_analysis(args.data, args.output)
    
    if success:
        print(f"\n🎉 分析成功完成！")
        print(f"💡 建议:")
        print(f"1. 查看输出目录中的CSV文件")
        print(f"2. 根据信号表现调整参数")
        print(f"3. 在实际数据上验证效果")
    else:
        print(f"\n💥 分析失败，请检查错误信息")
    
    return success

if __name__ == "__main__":
    # 使用示例:
    # python run_plate_pressure_analysis.py                           # 使用样本数据，仅压单分析
    # python run_plate_pressure_analysis.py --mode integrated         # 使用样本数据，综合分析
    # python run_plate_pressure_analysis.py your_data.csv             # 使用指定数据，仅压单分析
    # python run_plate_pressure_analysis.py your_data.csv --mode integrated  # 使用指定数据，综合分析
    main()
