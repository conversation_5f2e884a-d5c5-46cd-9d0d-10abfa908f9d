"""
盘口压单功能测试脚本
验证Level 2数据盘口压单分析功能
"""

import pandas as pd
import numpy as np
import sys
import os
from datetime import datetime

# 添加当前目录到路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def create_pressure_test_data():
    """创建专门用于测试压单的数据"""
    n_records = 30
    base_price = 149.15
    
    data = {
        'id': list(range(1, n_records + 1)),
        'symbol': ['solusdt'] * n_records,
        'timestamp': [],
        'update_id': [],
        'bids_json': [],
        'asks_json': [],
        'best_bid_price': [],
        'best_ask_price': [],
        'bid_volume': [],
        'ask_volume': [],
        'spread': [],
        'spread_percent': [],
        'created_at': ['08:02.0'] * n_records
    }
    
    # 生成时间序列
    base_time = datetime(2025, 4, 27, 8, 2, 0)
    for i in range(n_records):
        timestamp = base_time.replace(second=i)
        data['timestamp'].append(timestamp.strftime('%Y-%m-%d %H:%M:%S'))
        data['update_id'].append(7367563376294 + i * 1000)
    
    # 生成特定的压单场景
    scenarios = [
        'normal',      # 正常盘口
        'buy_pressure', # 买盘压单
        'sell_pressure', # 卖盘压单
        'balanced'     # 平衡盘口
    ]
    
    for i in range(n_records):
        current_price = base_price + (i % 5 - 2) * 0.01
        bid_price = current_price - 0.01
        ask_price = current_price
        
        # 根据位置选择场景
        scenario = scenarios[i % len(scenarios)]
        
        # 生成买盘数据
        bids = []
        total_bid_volume = 0
        
        for j in range(5):  # 5档买盘
            price = round(bid_price - j * 0.01, 2)
            
            if scenario == 'buy_pressure':
                # 买盘压单：前几档买量特别大
                volume = round(2000 * (1.5 ** (5-j)) + np.random.normal(0, 100), 2)
            elif scenario == 'sell_pressure':
                # 卖盘压单时买盘相对较小
                volume = round(500 + j * 50 + np.random.normal(0, 50), 2)
            else:
                # 正常或平衡盘口
                volume = round(1000 + j * 100 + np.random.normal(0, 100), 2)
            
            volume = max(volume, 10)  # 确保最小量
            bids.append([price, volume])
            total_bid_volume += volume
        
        # 生成卖盘数据
        asks = []
        total_ask_volume = 0
        
        for j in range(5):  # 5档卖盘
            price = round(ask_price + j * 0.01, 2)
            
            if scenario == 'sell_pressure':
                # 卖盘压单：前几档卖量特别大
                volume = round(1500 * (1.5 ** (5-j)) + np.random.normal(0, 100), 2)
            elif scenario == 'buy_pressure':
                # 买盘压单时卖盘相对较小
                volume = round(300 + j * 30 + np.random.normal(0, 30), 2)
            else:
                # 正常或平衡盘口
                volume = round(600 + j * 60 + np.random.normal(0, 60), 2)
            
            volume = max(volume, 5)  # 确保最小量
            asks.append([price, volume])
            total_ask_volume += volume
        
        # 转换为JSON字符串
        data['bids_json'].append(str(bids).replace("'", '"'))
        data['asks_json'].append(str(asks).replace("'", '"'))
        
        data['best_bid_price'].append(bids[0][0])
        data['best_ask_price'].append(asks[0][0])
        data['bid_volume'].append(total_bid_volume)
        data['ask_volume'].append(total_ask_volume)
        data['spread'].append(round(asks[0][0] - bids[0][0], 4))
        data['spread_percent'].append(round((asks[0][0] - bids[0][0]) / current_price * 100, 6))
    
    return pd.DataFrame(data)

def test_plate_pressure_analyzer():
    """测试盘口压单分析器"""
    print("=" * 60)
    print("测试盘口压单分析器")
    print("=" * 60)
    
    try:
        from level2_plate_pressure import Level2PlatePressureAnalyzer
        
        # 创建测试数据
        df = create_pressure_test_data()
        print(f"✓ 创建测试数据: {len(df)} 条记录")
        
        # 初始化分析器
        analyzer = Level2PlatePressureAnalyzer(df, plate_percent=0.002, max_depth=5)
        print("✓ 压单分析器初始化成功")
        
        # 计算阻力
        analyzer.calculate_resistance()
        print("✓ 阻力计算完成")
        
        # 生成信号
        analyzer.generate_plate_signals(ratio_low=2.0, ratio_high=8.0)
        print("✓ 压单信号生成完成")
        
        # 获取结果
        results = analyzer.get_analysis_results()
        
        print(f"\n📊 分析结果:")
        print(f"  价格宽度: {results['price_width']:.4f}")
        print(f"  多头压单信号: {results['signal_stats']['long_signals']} 个")
        print(f"  空头压单信号: {results['signal_stats']['short_signals']} 个")
        print(f"  信号率: {results['signal_stats']['signal_rate']:.2%}")
        
        # 显示前几个tick的详细信息
        print(f"\n📋 前5个tick的压单分析:")
        print(f"{'Tick':<4} {'买盘阻力':<10} {'卖盘阻力':<10} {'多头信号':<8} {'空头信号':<8}")
        print("-" * 50)
        
        for i in range(min(5, len(analyzer.long_resist_list))):
            long_resist = analyzer.long_resist_list[i]
            short_resist = analyzer.short_resist_list[i]
            long_signal = analyzer.long_plate_signals[i]
            short_signal = analyzer.short_plate_signals[i]
            
            print(f"{i+1:<4} {long_resist:<10.2f} {short_resist:<10.2f} {long_signal:<8} {short_signal:<8}")
        
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_integrated_analyzer():
    """测试综合分析器"""
    print("\n" + "=" * 60)
    print("测试综合分析器（动量+压单）")
    print("=" * 60)
    
    try:
        from integrated_level2_analyzer import IntegratedLevel2Analyzer
        
        # 创建测试数据
        df = create_pressure_test_data()
        
        # 配置参数
        config = {
            'momentum_window': 1.0,
            'momentum_price_width': 0.001,
            'momentum_threshold': 0.01,
            'plate_percent': 0.002,
            'max_depth': 5,
            'plate_ratio_low': 2.0,
            'plate_ratio_high': 8.0,
            'twap_duration': 30,
            'signal_duration': 15,
            'signal_combination_mode': 'AND'
        }
        
        # 初始化综合分析器
        analyzer = IntegratedLevel2Analyzer(df, config)
        print("✓ 综合分析器初始化成功")
        
        # 运行分析
        results = analyzer.run_full_analysis()
        print("✓ 综合分析完成")
        
        # 显示结果摘要
        if 'integrated_signals' in results and results['integrated_signals']:
            integrated = results['integrated_signals']
            long_count = sum(integrated['long_signals'])
            short_count = sum(integrated['short_signals'])
            
            print(f"\n📈 综合信号结果:")
            print(f"  多头综合信号: {long_count} 个")
            print(f"  空头综合信号: {short_count} 个")
            print(f"  总信号: {long_count + short_count} 个")
        
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_signal_performance():
    """测试信号表现计算"""
    print("\n" + "=" * 60)
    print("测试信号表现计算")
    print("=" * 60)
    
    try:
        from level2_plate_pressure import Level2PlatePressureAnalyzer
        
        # 创建更多数据用于表现测试
        df = create_pressure_test_data()
        
        # 扩展数据以便有足够的时间计算TWAP
        extended_data = []
        for i in range(3):  # 重复3次数据
            temp_df = df.copy()
            # 调整时间戳
            temp_df['timestamp'] = pd.to_datetime(temp_df['timestamp']) + pd.Timedelta(seconds=len(df)*i)
            temp_df['timestamp'] = temp_df['timestamp'].dt.strftime('%Y-%m-%d %H:%M:%S')
            extended_data.append(temp_df)
        
        extended_df = pd.concat(extended_data, ignore_index=True)
        print(f"✓ 创建扩展测试数据: {len(extended_df)} 条记录")
        
        # 初始化分析器
        analyzer = Level2PlatePressureAnalyzer(extended_df, plate_percent=0.002, max_depth=5)
        
        # 运行分析
        analyzer.calculate_resistance()
        analyzer.generate_plate_signals(ratio_low=2.0, ratio_high=6.0)
        
        # 计算信号表现
        long_profits, short_profits = analyzer.calculate_signal_performance(
            twap_duration=30, signal_duration=15
        )
        
        print(f"✓ 信号表现计算完成")
        
        if long_profits:
            print(f"\n📈 多头信号表现:")
            print(f"  信号数量: {len(long_profits)}")
            print(f"  平均收益: {np.mean(long_profits):.2f} bp")
            print(f"  收益标准差: {np.std(long_profits):.2f} bp")
            print(f"  最大收益: {max(long_profits):.2f} bp")
            print(f"  最小收益: {min(long_profits):.2f} bp")
        else:
            print(f"  无多头信号")
        
        if short_profits:
            print(f"\n📉 空头信号表现:")
            print(f"  信号数量: {len(short_profits)}")
            print(f"  平均收益: {np.mean(short_profits):.2f} bp")
            print(f"  收益标准差: {np.std(short_profits):.2f} bp")
            print(f"  最大收益: {max(short_profits):.2f} bp")
            print(f"  最小收益: {min(short_profits):.2f} bp")
        else:
            print(f"  无空头信号")
        
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_csv_export():
    """测试CSV导出功能"""
    print("\n" + "=" * 60)
    print("测试CSV导出功能")
    print("=" * 60)
    
    try:
        from level2_plate_pressure import Level2PlatePressureAnalyzer
        
        # 创建测试数据
        df = create_pressure_test_data()
        
        # 运行分析
        analyzer = Level2PlatePressureAnalyzer(df, plate_percent=0.002, max_depth=5)
        analyzer.calculate_resistance()
        analyzer.generate_plate_signals(ratio_low=2.0, ratio_high=8.0)
        
        # 导出结果
        output_file = analyzer.export_results('test_pressure_results.csv')
        print(f"✓ 结果已导出到: {output_file}")
        
        # 验证文件
        if os.path.exists(output_file):
            result_df = pd.read_csv(output_file)
            print(f"✓ CSV文件验证成功:")
            print(f"  行数: {len(result_df)}")
            print(f"  列数: {len(result_df.columns)}")
            print(f"  列名: {list(result_df.columns)}")
            
            # 显示前几行
            print(f"\n📋 前3行数据:")
            print(result_df.head(3).to_string(index=False))
            
            return True
        else:
            print(f"❌ CSV文件未找到")
            return False
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主测试函数"""
    print("🚀 盘口压单功能测试")
    print("=" * 60)
    
    # 运行各项测试
    test1 = test_plate_pressure_analyzer()
    test2 = test_integrated_analyzer()
    test3 = test_signal_performance()
    test4 = test_csv_export()
    
    # 总结测试结果
    print("\n" + "=" * 60)
    print("测试结果总结")
    print("=" * 60)
    
    tests = [
        ("盘口压单分析器", test1),
        ("综合分析器", test2),
        ("信号表现计算", test3),
        ("CSV导出功能", test4)
    ]
    
    passed = 0
    for name, result in tests:
        status = "✅ 通过" if result else "❌ 失败"
        print(f"{name:<20} {status}")
        if result:
            passed += 1
    
    print(f"\n总体结果: {passed}/{len(tests)} 项测试通过")
    
    if passed == len(tests):
        print("🎉 所有测试通过！盘口压单功能正常。")
        print("\n💡 下一步:")
        print("1. 使用真实Level 2数据进行测试")
        print("2. 调整参数以优化信号质量")
        print("3. 集成到交易策略中")
    else:
        print("⚠️  部分测试失败，请检查错误信息。")
    
    return passed == len(tests)

if __name__ == "__main__":
    success = main()
