# CSV输出使用说明

## 📋 概述

修改后的 `main_level2_analysis.py` 现在会自动将所有分析结果输出到CSV文件，无需额外配置。

## 🚀 快速使用

### 方法1：使用专用脚本（推荐）

```bash
# 使用样本数据
python run_analysis_csv.py

# 使用自己的数据文件  
python run_analysis_csv.py your_level2_data.csv
```

### 方法2：使用主程序

```bash
# 使用样本数据
python main_level2_analysis.py --demo

# 使用数据文件
python main_level2_analysis.py --data your_data.csv --output results/
```

### 方法3：Python代码调用

```python
from main_level2_analysis import Level2AnalysisMain, create_sample_data

# 使用样本数据
df = create_sample_data()
analyzer = Level2AnalysisMain(df)
results, saved_files = analyzer.run_full_analysis()

print(f"保存了 {len(saved_files)} 个文件:")
for file in saved_files:
    print(f"  - {file}")
```

## 📊 输出文件说明

运行分析后，会在输出目录生成以下文件：

### 1. `momentum_results_YYYYMMDD_HHMMSS.csv`
**核心动量信号文件**
- `timestamp`: 时间戳
- `symbol`: 交易品种
- `mid_price`: 中间价
- `momentum`: 动量值
- `long_signal`: 多头信号(1=有信号, 0=无信号)
- `short_signal`: 空头信号(1=有信号, 0=无信号)

### 2. `detailed_analysis_YYYYMMDD_HHMMSS.csv`
**详细分析文件**
- `timestamp`: 时间戳
- `symbol`: 交易品种
- `mid_price`: 中间价
- `momentum_value`: 动量值
- `long_signal`: 多头信号
- `short_signal`: 空头信号
- `signal_type`: 信号类型("Long"/"Short"/"None")
- `abs_momentum`: 绝对动量值
- `price_change`: 价格变化
- `price_change_pct`: 价格变化百分比
- `momentum_rank`: 动量排名(0-1百分位)

### 3. `summary_stats_YYYYMMDD_HHMMSS.csv`
**统计汇总文件**
- `metric`: 指标名称
- `value`: 指标值
- `category`: 指标分类

### 4. `analysis_summary_YYYYMMDD_HHMMSS.txt`
**文本报告文件**
- 完整的分析报告
- 动量统计信息
- 价格统计信息

## 📈 CSV文件使用示例

### 读取动量信号
```python
import pandas as pd

# 读取动量结果
df = pd.read_csv('momentum_results_20250729_143022.csv')

# 查看多头信号
long_signals = df[df['long_signal'] == 1]
print(f"多头信号数量: {len(long_signals)}")

# 查看空头信号
short_signals = df[df['short_signal'] == 1]
print(f"空头信号数量: {len(short_signals)}")

# 分析动量分布
print(f"动量均值: {df['momentum'].mean():.6f}")
print(f"动量标准差: {df['momentum'].std():.6f}")
```

### 分析详细数据
```python
# 读取详细分析
df_detail = pd.read_csv('detailed_analysis_20250729_143022.csv')

# 按信号类型分组
signal_summary = df_detail.groupby('signal_type').agg({
    'momentum_value': ['count', 'mean', 'std'],
    'price_change_pct': ['mean', 'std']
})
print(signal_summary)

# 查看最强动量信号
top_momentum = df_detail.nlargest(10, 'abs_momentum')
print("最强动量信号:")
print(top_momentum[['timestamp', 'signal_type', 'momentum_value', 'price_change_pct']])
```

## 🔧 配置选项

可以通过配置参数自定义输出：

```python
config = {
    'momentum_window': 1.0,          # 动量计算窗口(秒)
    'price_width_percent': 0.001,    # 价格宽度百分比
    'momentum_threshold': 0.01,      # 动量信号阈值
    'max_depth': 10,                 # 最大显示档位
    'output_dir': 'my_results',      # 自定义输出目录
    'save_plots': False,             # 不生成图表
    'save_results': True,            # 强制保存结果
    'force_save': True               # 强制保存
}

analyzer = Level2AnalysisMain(data, config)
```

## 📁 输出目录结构

```
output/  (或自定义目录)
├── momentum_results_20250729_143022.csv      # 核心动量信号
├── detailed_analysis_20250729_143022.csv     # 详细分析数据
├── summary_stats_20250729_143022.csv         # 统计汇总
├── analysis_summary_20250729_143022.txt      # 文本报告
├── orderbook_depth_20250729_143022.png       # 盘口深度图(可选)
└── price_momentum_timeline_20250729_143022.png # 时间序列图(可选)
```

## ⚡ 性能优化建议

### 大数据量处理
```python
# 对于大量数据，可以关闭图表生成以提高速度
config = {
    'save_plots': False,  # 关闭图表生成
    'save_results': True  # 只保存CSV
}
```

### 批量处理
```python
import glob

# 批量处理多个文件
data_files = glob.glob('data/*.csv')
for file in data_files:
    print(f"处理文件: {file}")
    analyzer = Level2AnalysisMain(file)
    results, saved_files = analyzer.run_full_analysis()
    print(f"完成，保存了 {len(saved_files)} 个文件")
```

## 🐛 常见问题

### Q1: 没有生成CSV文件
**A**: 检查以下几点：
- 确保 `save_results=True`
- 检查输出目录权限
- 查看控制台错误信息

### Q2: CSV文件为空
**A**: 可能原因：
- 输入数据格式不正确
- 动量阈值设置过高
- 数据量太少

### Q3: 内存不足
**A**: 对于大数据量：
- 设置 `save_plots=False`
- 分批处理数据
- 增加系统内存

## 📞 技术支持

如有问题，请检查：
1. 数据格式是否符合要求
2. 依赖库是否正确安装
3. Python版本兼容性
4. 文件权限设置

## 🎯 下一步

1. 使用Excel或其他工具打开CSV文件
2. 进行进一步的数据分析和可视化
3. 根据动量信号制定交易策略
4. 回测信号效果
