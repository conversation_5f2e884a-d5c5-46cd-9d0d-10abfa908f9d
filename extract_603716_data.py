# -*- coding: utf-8 -*-
"""
专门用于解压603716.SH数据的脚本 (WSL Linux版本)
从Windows路径下的7z文件中解压20250701/603716.SH目录
并创建20250701-20250731的目录结构
"""

import os
import subprocess
import shutil
from datetime import datetime, timedelta
import logging

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(message)s')

def check_dependencies():
    """检查必要的依赖"""
    print("检查系统依赖...")

    # 检查p7zip是否安装
    try:
        result = subprocess.run(['7z'], capture_output=True, text=True)
        print("✓ 7z 已安装")
        return True
    except FileNotFoundError:
        print("✗ 7z 未安装")
        print("请安装p7zip: sudo apt update && sudo apt install p7zip-full")
        return False

def convert_windows_path_to_wsl(windows_path):
    """将Windows路径转换为WSL路径"""
    # D:\BaiduNetdiskDownload\202507 -> /mnt/d/BaiduNetdiskDownload/202507
    if windows_path.startswith('D:\\'):
        wsl_path = windows_path.replace('D:\\', '/mnt/d/').replace('\\', '/')
        return wsl_path
    elif windows_path.startswith('C:\\'):
        wsl_path = windows_path.replace('C:\\', '/mnt/c/').replace('\\', '/')
        return wsl_path
    else:
        # 假设是D盘
        wsl_path = '/mnt/d/' + windows_path.replace('\\', '/')
        return wsl_path

def extract_603716_data():
    """解压603716.SH数据并创建目录结构"""

    # 检查依赖
    if not check_dependencies():
        return

    # 配置路径 (Windows路径)
    windows_source_dir = r"D:\BaiduNetdiskDownload\202507"
    windows_output_dir = r"D:\BaiduNetdiskDownload\202507\603716_SH_data"

    # 转换为WSL路径
    source_dir = convert_windows_path_to_wsl(windows_source_dir)
    output_dir = convert_windows_path_to_wsl(windows_output_dir)
    target_path = "20250701/603716.SH"

    print("开始处理603716.SH数据解压...")
    print(f"Windows源目录: {windows_source_dir}")
    print(f"WSL源目录: {source_dir}")
    print(f"WSL输出目录: {output_dir}")

    # 检查源目录是否存在
    if not os.path.exists(source_dir):
        print(f"错误: 源目录不存在 {source_dir}")
        print("请确保Windows路径正确，并且WSL可以访问该路径")
        return

    # 创建输出目录
    os.makedirs(output_dir, exist_ok=True)
    
    # 创建日期目录 20250701-20250731
    print("\n创建日期目录...")
    create_date_directories(output_dir)
    
    # 查找7z文件
    zip_files = [f for f in os.listdir(source_dir) if f.lower().endswith('.7z')]
    
    if not zip_files:
        print("未找到7z文件！")
        return
    
    print(f"\n找到 {len(zip_files)} 个7z文件")
    
    # 处理每个7z文件
    for i, zip_file in enumerate(zip_files, 1):
        print(f"\n[{i}/{len(zip_files)}] 处理: {zip_file}")
        zip_path = os.path.join(source_dir, zip_file)
        
        # 解压到临时目录
        temp_dir = os.path.join(output_dir, "temp_extract")
        
        if extract_from_7z(zip_path, temp_dir, target_path):
            # 移动解压的文件到正确位置
            move_extracted_files(temp_dir, output_dir, target_path)
        
        # 清理临时目录
        if os.path.exists(temp_dir):
            shutil.rmtree(temp_dir)
    
    print("\n解压完成！")

def create_date_directories(base_dir):
    """创建20250701-20250731的目录"""
    start_date = datetime(2025, 7, 1)
    end_date = datetime(2025, 7, 31)
    
    current = start_date
    while current <= end_date:
        date_str = current.strftime("%Y%m%d")
        date_dir = os.path.join(base_dir, date_str, "603716.SH")
        os.makedirs(date_dir, exist_ok=True)
        print(f"创建目录: {date_dir}")
        current += timedelta(days=1)

def extract_from_7z(zip_file, extract_dir, target_path):
    """从7z文件解压指定路径 (Linux版本)"""
    try:
        os.makedirs(extract_dir, exist_ok=True)

        # Linux下的7z解压命令
        cmd = [
            '7z', 'x',
            zip_file,
            f"{target_path}/*",
            f'-o{extract_dir}',
            '-y'
        ]

        print(f"  执行命令: {' '.join(cmd)}")
        result = subprocess.run(cmd, capture_output=True, text=True)

        if result.returncode == 0:
            print(f"  ✓ 解压成功")
            return True
        else:
            print(f"  ✗ 解压失败")
            print(f"  错误输出: {result.stderr}")
            print(f"  标准输出: {result.stdout}")
            return False

    except Exception as e:
        print(f"  ✗ 解压出错: {str(e)}")
        return False

def move_extracted_files(temp_dir, output_dir, target_path):
    """移动解压的文件到正确位置"""
    source_path = os.path.join(temp_dir, target_path)
    
    if not os.path.exists(source_path):
        print(f"  警告: 未找到目标路径 {target_path}")
        return
    
    # 获取文件列表
    files = os.listdir(source_path)
    if not files:
        print("  警告: 目标目录为空")
        return
    
    print(f"  找到 {len(files)} 个文件")
    
    # 根据文件名中的日期信息移动文件
    for file in files:
        # 尝试从文件名中提取日期
        date_str = extract_date_from_filename(file)
        
        if date_str:
            target_dir = os.path.join(output_dir, date_str, "603716.SH")
            os.makedirs(target_dir, exist_ok=True)
            
            source_file = os.path.join(source_path, file)
            target_file = os.path.join(target_dir, file)
            
            try:
                shutil.copy2(source_file, target_file)
                print(f"    移动: {file} -> {date_str}/603716.SH/")
            except Exception as e:
                print(f"    错误: 移动文件失败 {file}: {str(e)}")
        else:
            # 如果无法提取日期，放到20250701目录
            default_dir = os.path.join(output_dir, "20250701", "603716.SH")
            os.makedirs(default_dir, exist_ok=True)
            
            source_file = os.path.join(source_path, file)
            target_file = os.path.join(default_dir, file)
            
            try:
                shutil.copy2(source_file, target_file)
                print(f"    移动: {file} -> 20250701/603716.SH/ (默认)")
            except Exception as e:
                print(f"    错误: 移动文件失败 {file}: {str(e)}")

def extract_date_from_filename(filename):
    """从文件名中提取日期"""
    import re
    
    # 尝试匹配YYYYMMDD格式的日期
    date_pattern = r'(202507\d{2})'
    match = re.search(date_pattern, filename)
    
    if match:
        return match.group(1)
    
    # 如果没有找到日期，返回None
    return None

def test_wsl_environment():
    """测试WSL环境"""
    print("测试WSL环境...")

    # 测试路径转换
    test_path = r"D:\BaiduNetdiskDownload\202507"
    wsl_path = convert_windows_path_to_wsl(test_path)
    print(f"路径转换测试: {test_path} -> {wsl_path}")

    # 测试路径是否存在
    if os.path.exists(wsl_path):
        print(f"✓ 路径可访问: {wsl_path}")

        # 列出目录内容
        try:
            files = os.listdir(wsl_path)
            zip_files = [f for f in files if f.lower().endswith('.7z')]
            print(f"✓ 找到 {len(zip_files)} 个7z文件")
            if zip_files:
                print("  7z文件列表:")
                for f in zip_files[:5]:  # 只显示前5个
                    print(f"    - {f}")
                if len(zip_files) > 5:
                    print(f"    ... 还有 {len(zip_files) - 5} 个文件")
        except Exception as e:
            print(f"✗ 无法列出目录内容: {e}")
    else:
        print(f"✗ 路径不可访问: {wsl_path}")
        print("请检查:")
        print("1. Windows路径是否正确")
        print("2. WSL是否可以访问Windows文件系统")
        print("3. 尝试在WSL中运行: ls /mnt/d/")

def main():
    """主函数"""
    print("603716.SH数据解压工具 (WSL Linux版本)")
    print("=" * 50)

    # 测试环境
    test_wsl_environment()
    print()

    # 确认执行
    confirm = input("是否开始解压？(y/n): ").lower().strip()
    if confirm == 'y':
        extract_603716_data()
    else:
        print("操作已取消")

if __name__ == "__main__":
    main()
