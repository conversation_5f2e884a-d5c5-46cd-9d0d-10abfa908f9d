# -*- coding: utf-8 -*-
"""
专门用于解压603716.SH数据的脚本
从D:\BaiduNetdiskDownload\202507下的7z文件中解压20250701\603716.SH目录
并创建20250701-20250731的目录结构
"""

import os
import subprocess
import shutil
from datetime import datetime, timedelta
import logging

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(message)s')

def extract_603716_data():
    """解压603716.SH数据并创建目录结构"""
    
    # 配置路径
    source_dir = r"D:\BaiduNetdiskDownload\202507"
    output_dir = r"D:\BaiduNetdiskDownload\202507\603716_SH_data"
    target_path = "20250701/603716.SH"
    
    print("开始处理603716.SH数据解压...")
    print(f"源目录: {source_dir}")
    print(f"输出目录: {output_dir}")
    
    # 创建输出目录
    os.makedirs(output_dir, exist_ok=True)
    
    # 创建日期目录 20250701-20250731
    print("\n创建日期目录...")
    create_date_directories(output_dir)
    
    # 查找7z文件
    zip_files = [f for f in os.listdir(source_dir) if f.lower().endswith('.7z')]
    
    if not zip_files:
        print("未找到7z文件！")
        return
    
    print(f"\n找到 {len(zip_files)} 个7z文件")
    
    # 处理每个7z文件
    for i, zip_file in enumerate(zip_files, 1):
        print(f"\n[{i}/{len(zip_files)}] 处理: {zip_file}")
        zip_path = os.path.join(source_dir, zip_file)
        
        # 解压到临时目录
        temp_dir = os.path.join(output_dir, "temp_extract")
        
        if extract_from_7z(zip_path, temp_dir, target_path):
            # 移动解压的文件到正确位置
            move_extracted_files(temp_dir, output_dir, target_path)
        
        # 清理临时目录
        if os.path.exists(temp_dir):
            shutil.rmtree(temp_dir)
    
    print("\n解压完成！")

def create_date_directories(base_dir):
    """创建20250701-20250731的目录"""
    start_date = datetime(2025, 7, 1)
    end_date = datetime(2025, 7, 31)
    
    current = start_date
    while current <= end_date:
        date_str = current.strftime("%Y%m%d")
        date_dir = os.path.join(base_dir, date_str, "603716.SH")
        os.makedirs(date_dir, exist_ok=True)
        print(f"创建目录: {date_dir}")
        current += timedelta(days=1)

def extract_from_7z(zip_file, extract_dir, target_path):
    """从7z文件解压指定路径"""
    try:
        # 检查7z命令是否可用
        try:
            subprocess.run(['7z'], capture_output=True)
        except FileNotFoundError:
            print("错误: 未找到7z命令，请安装7-Zip")
            return False
        
        os.makedirs(extract_dir, exist_ok=True)
        
        # 7z解压命令
        cmd = [
            '7z', 'x',
            zip_file,
            f"{target_path}/*",
            f'-o{extract_dir}',
            '-y'
        ]
        
        result = subprocess.run(cmd, capture_output=True, text=True)
        
        if result.returncode == 0:
            print(f"  ✓ 解压成功")
            return True
        else:
            print(f"  ✗ 解压失败: {result.stderr}")
            return False
            
    except Exception as e:
        print(f"  ✗ 解压出错: {str(e)}")
        return False

def move_extracted_files(temp_dir, output_dir, target_path):
    """移动解压的文件到正确位置"""
    source_path = os.path.join(temp_dir, target_path)
    
    if not os.path.exists(source_path):
        print(f"  警告: 未找到目标路径 {target_path}")
        return
    
    # 获取文件列表
    files = os.listdir(source_path)
    if not files:
        print("  警告: 目标目录为空")
        return
    
    print(f"  找到 {len(files)} 个文件")
    
    # 根据文件名中的日期信息移动文件
    for file in files:
        # 尝试从文件名中提取日期
        date_str = extract_date_from_filename(file)
        
        if date_str:
            target_dir = os.path.join(output_dir, date_str, "603716.SH")
            os.makedirs(target_dir, exist_ok=True)
            
            source_file = os.path.join(source_path, file)
            target_file = os.path.join(target_dir, file)
            
            try:
                shutil.copy2(source_file, target_file)
                print(f"    移动: {file} -> {date_str}/603716.SH/")
            except Exception as e:
                print(f"    错误: 移动文件失败 {file}: {str(e)}")
        else:
            # 如果无法提取日期，放到20250701目录
            default_dir = os.path.join(output_dir, "20250701", "603716.SH")
            os.makedirs(default_dir, exist_ok=True)
            
            source_file = os.path.join(source_path, file)
            target_file = os.path.join(default_dir, file)
            
            try:
                shutil.copy2(source_file, target_file)
                print(f"    移动: {file} -> 20250701/603716.SH/ (默认)")
            except Exception as e:
                print(f"    错误: 移动文件失败 {file}: {str(e)}")

def extract_date_from_filename(filename):
    """从文件名中提取日期"""
    import re
    
    # 尝试匹配YYYYMMDD格式的日期
    date_pattern = r'(202507\d{2})'
    match = re.search(date_pattern, filename)
    
    if match:
        return match.group(1)
    
    # 如果没有找到日期，返回None
    return None

def main():
    """主函数"""
    print("603716.SH数据解压工具")
    print("=" * 40)
    
    # 检查源目录是否存在
    source_dir = r"D:\BaiduNetdiskDownload\202507"
    if not os.path.exists(source_dir):
        print(f"错误: 源目录不存在 {source_dir}")
        return
    
    # 确认执行
    confirm = input("是否开始解压？(y/n): ").lower().strip()
    if confirm == 'y':
        extract_603716_data()
    else:
        print("操作已取消")

if __name__ == "__main__":
    main()
