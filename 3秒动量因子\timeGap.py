point = 0.000001

def getTime(time):

    time = str(int(time))
    second = int(time[-2:])
    minute = int(time[-4:-2])
    hour = int(time[:-4])

    return second,minute,hour

def timeGap(startTime,endTime):

    '''
    
    :param startTime: 开始时间
    :param endTime: 144957.0
    :return: 时间间隔，
    '''
    secondStart, minuteStart, hourStart = getTime(startTime)
    secondEnd, minuteEnd, hourEnd = getTime(endTime)

    secondBreak = 10
    minuteBreak = 30
    hourBreak = 11

    secondRestart = 50
    minuteRestart = 59
    hourRestart = 12

    gap1 =(hourBreak -hourStart)*3600 +(minuteBreak-minuteStart)*60 + (secondBreak-secondStart)

    gap2 = (hourEnd-hourRestart) * 3600 + (minuteEnd-minuteRestart) * 60 + (secondEnd-secondRestart)

    if gap1 > -point and gap2 > -point:


        return gap1 + gap2 - 23

    else:
        gap = (hourEnd -hourStart)*3600 +(minuteEnd-minuteStart)*60 + (secondEnd-secondStart)


    return gap


if __name__=='__main__':
    print(timeGap(93109.0,144957.0))