# WSL环境7z解压工具使用指南

## 📁 文件清单

已为您创建以下文件：

1. **`setup_wsl_environment.sh`** - 自动环境设置脚本
2. **`test_wsl_setup.py`** - WSL环境测试脚本  
3. **`extract_603716_data.py`** - 主解压脚本 (WSL版本)
4. **`run_extraction.sh`** - 一键运行脚本
5. **`README_WSL.md`** - 详细说明文档

## 🚀 快速开始 (3步完成)

### 步骤1: 设置环境
```bash
# 在WSL中运行
chmod +x setup_wsl_environment.sh
./setup_wsl_environment.sh
```

### 步骤2: 测试环境
```bash
python3 test_wsl_setup.py
```

### 步骤3: 开始解压
```bash
python3 extract_603716_data.py
```

## 🎯 一键运行 (推荐)
```bash
chmod +x run_extraction.sh
./run_extraction.sh
```

## 📋 功能特性

### ✅ 自动化功能
- 自动检测WSL环境
- 自动安装必要依赖 (p7zip-full)
- 自动路径转换 (Windows → WSL)
- 自动创建目录结构

### ✅ 智能处理
- 批量处理所有7z文件
- 智能文件分类 (根据日期)
- 错误处理和重试机制
- 详细的进度反馈

### ✅ 路径配置
- **源路径**: `D:\BaiduNetdiskDownload\202507` (Windows)
- **WSL路径**: `/mnt/d/BaiduNetdiskDownload/202507`
- **目标**: 解压 `20250701/603716.SH` 目录
- **输出**: 创建 `20250701-20250731` 目录结构

## 📊 输出结构

```
/mnt/d/BaiduNetdiskDownload/202507/603716_SH_data/
├── 20250701/
│   └── 603716.SH/
│       ├── 逐笔成交.csv
│       ├── 逐笔委托.csv
│       └── [其他文件]
├── 20250702/
│   └── 603716.SH/
│       └── [文件...]
...
└── 20250731/
    └── 603716.SH/
        └── [文件...]
```

## 🔧 环境要求

- **操作系统**: WSL (Windows Subsystem for Linux)
- **Linux发行版**: Ubuntu/Debian (推荐)
- **Python**: 3.x
- **工具**: p7zip-full

## ⚠️ 注意事项

1. **路径检查**: 确保 `D:\BaiduNetdiskDownload\202507` 在Windows中存在
2. **权限设置**: 脚本会自动设置必要权限
3. **磁盘空间**: 确保有足够空间存储解压文件
4. **网络连接**: 首次运行需要下载依赖包

## 🐛 故障排除

### 问题1: "7z命令未找到"
```bash
sudo apt update
sudo apt install p7zip-full
```

### 问题2: "路径不存在"
```bash
# 检查Windows路径挂载
ls /mnt/d/
ls /mnt/d/BaiduNetdiskDownload/
```

### 问题3: "权限被拒绝"
```bash
chmod +x *.sh
chmod +x *.py
```

### 问题4: "WSL环境问题"
- 确保在WSL中运行，不是Windows命令行
- 检查WSL版本: `wsl --version`
- 重启WSL: `wsl --shutdown` 然后重新打开

## 📝 使用示例

### 完整流程示例:
```bash
# 1. 克隆或下载脚本到WSL环境
cd ~
mkdir extract_tool
cd extract_tool

# 2. 设置权限
chmod +x *.sh *.py

# 3. 自动设置环境
./setup_wsl_environment.sh

# 4. 测试环境
python3 test_wsl_setup.py

# 5. 开始解压
python3 extract_603716_data.py
```

### 快速运行示例:
```bash
# 一键完成所有操作
./run_extraction.sh
# 选择选项3 (测试+解压)
```

## 📈 预期结果

成功运行后，您将得到：
- 31个日期目录 (20250701-20250731)
- 每个目录包含603716.SH子目录
- 所有相关的CSV文件按日期分类存储
- 详细的处理日志和统计信息

## 🔍 验证结果

```bash
# 检查输出目录
ls -la /mnt/d/BaiduNetdiskDownload/202507/603716_SH_data/

# 检查特定日期
ls -la /mnt/d/BaiduNetdiskDownload/202507/603716_SH_data/20250701/603716.SH/

# 统计文件数量
find /mnt/d/BaiduNetdiskDownload/202507/603716_SH_data/ -name "*.csv" | wc -l
```

## 📞 技术支持

如果遇到问题，请按以下顺序检查：
1. 运行 `test_wsl_setup.py` 诊断环境
2. 检查 `README_WSL.md` 详细说明
3. 查看脚本输出的错误信息
4. 确认Windows路径和WSL挂载状态
