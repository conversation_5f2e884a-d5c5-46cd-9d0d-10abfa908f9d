"""
简单测试脚本，验证1秒动量处理功能
"""

import pandas as pd
import numpy as np
import sys
import os

# 添加当前目录到路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def create_test_data():
    """创建测试数据"""
    sample_data = {
        'id': list(range(1, 11)),
        'symbol': ['solusdt'] * 10,
        'timestamp': [
            '2025-4-27 8:02:03', '2025-4-27 8:02:04', '2025-4-27 8:02:05', 
            '2025-4-27 8:02:06', '2025-4-27 8:02:07', '2025-4-27 8:02:08',
            '2025-4-27 8:02:09', '2025-4-27 8:02:10', '2025-4-27 8:02:11',
            '2025-4-27 8:02:12'
        ],
        'bids_json': [
            '[[149.14, 1014.48], [149.13, 883.52], [149.12, 708.73]]',
            '[[149.15, 1383.59], [149.14, 1018.94], [149.13, 706.59]]',
            '[[149.16, 1200.00], [149.15, 950.00], [149.14, 800.00]]',
            '[[149.17, 1100.00], [149.16, 900.00], [149.15, 750.00]]',
            '[[149.18, 1050.00], [149.17, 850.00], [149.16, 700.00]]',
            '[[149.19, 1000.00], [149.18, 800.00], [149.17, 650.00]]',
            '[[149.20, 950.00], [149.19, 750.00], [149.18, 600.00]]',
            '[[149.21, 900.00], [149.20, 700.00], [149.19, 550.00]]',
            '[[149.22, 850.00], [149.21, 650.00], [149.20, 500.00]]',
            '[[149.23, 800.00], [149.22, 600.00], [149.21, 450.00]]'
        ],
        'asks_json': [
            '[[149.15, 52.7], [149.16, 159.86], [149.17, 199.03]]',
            '[[149.16, 224.13], [149.17, 184.48], [149.18, 316.88]]',
            '[[149.17, 200.00], [149.18, 180.00], [149.19, 300.00]]',
            '[[149.18, 190.00], [149.19, 170.00], [149.20, 290.00]]',
            '[[149.19, 180.00], [149.20, 160.00], [149.21, 280.00]]',
            '[[149.20, 170.00], [149.21, 150.00], [149.22, 270.00]]',
            '[[149.21, 160.00], [149.22, 140.00], [149.23, 260.00]]',
            '[[149.22, 150.00], [149.23, 130.00], [149.24, 250.00]]',
            '[[149.23, 140.00], [149.24, 120.00], [149.25, 240.00]]',
            '[[149.24, 130.00], [149.25, 110.00], [149.26, 230.00]]'
        ],
        'best_bid_price': [149.14, 149.15, 149.16, 149.17, 149.18, 149.19, 149.20, 149.21, 149.22, 149.23],
        'best_ask_price': [149.15, 149.16, 149.17, 149.18, 149.19, 149.20, 149.21, 149.22, 149.23, 149.24],
        'bid_volume': [2606.73, 2708.12, 2950.00, 2750.00, 2600.00, 2450.00, 2300.00, 2150.00, 2000.00, 1850.00],
        'ask_volume': [411.59, 725.49, 680.00, 650.00, 620.00, 590.00, 560.00, 530.00, 500.00, 470.00],
        'spread': [0.01] * 10,
        'spread_percent': [0.0067] * 10,
        'created_at': ['03:16.0'] * 10
    }
    
    return pd.DataFrame(sample_data)

def test_momentum_processor():
    """测试动量处理器"""
    print("=== 测试1秒动量处理器 ===")
    
    try:
        from one_second_momentum_processor import OneSecondMomentumProcessor
        
        # 创建测试数据
        df = create_test_data()
        print(f"创建测试数据: {len(df)} 条记录")
        
        # 初始化处理器
        processor = OneSecondMomentumProcessor(df, momentum_window=1.0, price_width_percent=0.001)
        print("动量处理器初始化成功")
        
        # 计算动量信号
        signals = processor.calculate_momentum_signals(momentum_threshold=0.01)
        print("动量信号计算完成")
        
        # 获取统计信息
        stats = processor.get_signal_statistics()
        print("\n动量统计信息:")
        for key, value in stats.items():
            print(f"  {key}: {value}")
        
        # 显示前几个动量值
        print(f"\n前5个动量值: {signals['momentum_values'][:5]}")
        print(f"多头信号数: {sum(signals['long_signals'])}")
        print(f"空头信号数: {sum(signals['short_signals'])}")
        
        return True
        
    except Exception as e:
        print(f"动量处理器测试失败: {e}")
        return False

def test_orderbook_display():
    """测试盘口显示"""
    print("\n=== 测试盘口显示器 ===")
    
    try:
        from level2_orderbook_display import Level2OrderBookDisplay
        
        # 创建测试数据
        df = create_test_data()
        
        # 初始化显示器
        display = Level2OrderBookDisplay(df, max_depth=5)
        print("盘口显示器初始化成功")
        
        # 显示第一个盘口
        print("\n显示第一个盘口快照:")
        display.display_current_orderbook(0)
        
        # 生成汇总报告
        report = display.generate_summary_report()
        
        return True
        
    except Exception as e:
        print(f"盘口显示器测试失败: {e}")
        return False

def test_main_analysis():
    """测试主分析程序"""
    print("\n=== 测试主分析程序 ===")
    
    try:
        from main_level2_analysis import Level2AnalysisMain
        
        # 创建测试数据
        df = create_test_data()
        
        # 配置参数
        config = {
            'momentum_window': 1.0,
            'price_width_percent': 0.001,
            'momentum_threshold': 0.01,
            'max_depth': 5,
            'output_dir': 'test_output',
            'save_plots': False,  # 不保存图表避免显示问题
            'save_results': False  # 不保存结果
        }
        
        # 初始化分析器
        analyzer = Level2AnalysisMain(df, config)
        print("主分析器初始化成功")
        
        # 运行动量分析
        momentum_signals, momentum_stats = analyzer.run_momentum_analysis()
        print("动量分析完成")
        
        # 运行盘口分析
        summary_report = analyzer.run_orderbook_analysis()
        print("盘口分析完成")
        
        # 显示样本盘口
        print("\n显示样本盘口:")
        analyzer.display_sample_orderbook(0)
        
        return True
        
    except Exception as e:
        print(f"主分析程序测试失败: {e}")
        return False

def main():
    """主测试函数"""
    print("开始测试1秒动量分析系统...")
    
    # 测试各个模块
    test1 = test_momentum_processor()
    test2 = test_orderbook_display()
    test3 = test_main_analysis()
    
    # 总结
    print(f"\n{'='*50}")
    print("测试结果总结:")
    print(f"动量处理器: {'通过' if test1 else '失败'}")
    print(f"盘口显示器: {'通过' if test2 else '失败'}")
    print(f"主分析程序: {'通过' if test3 else '失败'}")
    
    if all([test1, test2, test3]):
        print("\n✅ 所有测试通过！系统运行正常。")
    else:
        print("\n❌ 部分测试失败，请检查错误信息。")
    
    print(f"{'='*50}")

if __name__ == "__main__":
    main()
