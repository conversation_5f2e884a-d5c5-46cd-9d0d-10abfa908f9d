"""
1秒动量因子处理器
基于Level 2数据生成1秒动量信号

数据格式:
id, symbol, timestamp, update_id, bids_json, asks_json, best_bid_price, best_ask_price, 
bid_volume, ask_volume, spread, spread_percent, created_at
"""

import pandas as pd
import numpy as np
import json
from datetime import datetime, timedelta
import warnings
warnings.filterwarnings('ignore')

class OneSecondMomentumProcessor:
    
    def __init__(self, data_df, momentum_window=1.0, price_width_percent=0.001):
        """
        初始化1秒动量处理器
        
        Args:
            data_df: Level 2数据DataFrame
            momentum_window: 动量计算窗口(秒)，默认1秒
            price_width_percent: 价格宽度百分比，默认0.1%
        """
        self.data_df = data_df.copy()
        self.momentum_window = momentum_window
        self.price_width_percent = price_width_percent
        
        # 处理后的数据
        self.processed_data = []
        self.momentum_signals = []
        
        # 初始化处理
        self._preprocess_data()
    
    def _preprocess_data(self):
        """预处理Level 2数据"""
        print("开始预处理Level 2数据...")
        
        # 转换时间戳
        self.data_df['timestamp_dt'] = pd.to_datetime(self.data_df['timestamp'])
        self.data_df = self.data_df.sort_values('timestamp_dt').reset_index(drop=True)
        
        # 解析JSON格式的买卖盘数据
        for idx, row in self.data_df.iterrows():
            try:
                # 解析买卖盘JSON数据
                bids_data = json.loads(row['bids_json']) if isinstance(row['bids_json'], str) else row['bids_json']
                asks_data = json.loads(row['asks_json']) if isinstance(row['asks_json'], str) else row['asks_json']
                
                # 提取价格和数量
                bid_prices = [float(bid[0]) for bid in bids_data]
                bid_volumes = [float(bid[1]) for bid in bids_data]
                ask_prices = [float(ask[0]) for ask in asks_data]
                ask_volumes = [float(ask[1]) for ask in asks_data]
                
                processed_tick = {
                    'timestamp': row['timestamp_dt'],
                    'symbol': row['symbol'],
                    'bid_prices': bid_prices,
                    'bid_volumes': bid_volumes,
                    'ask_prices': ask_prices,
                    'ask_volumes': ask_volumes,
                    'best_bid': float(row['best_bid_price']),
                    'best_ask': float(row['best_ask_price']),
                    'total_bid_volume': float(row['bid_volume']),
                    'total_ask_volume': float(row['ask_volume']),
                    'spread': float(row['spread']),
                    'mid_price': (float(row['best_bid_price']) + float(row['best_ask_price'])) / 2
                }
                
                self.processed_data.append(processed_tick)
                
            except Exception as e:
                print(f"处理第{idx}行数据时出错: {e}")
                continue
        
        print(f"成功处理{len(self.processed_data)}条数据")
    
    def _calculate_price_width(self, mid_price):
        """计算价格宽度"""
        return mid_price * self.price_width_percent
    
    def _get_volume_in_range(self, prices, volumes, center_price, width, is_bid=True):
        """获取指定价格范围内的总量"""
        total_volume = 0
        
        for price, volume in zip(prices, volumes):
            if price <= 0:  # 跳过无效价格
                continue
                
            if is_bid:
                # 买盘：价格在center_price - width以上
                if price >= center_price - width:
                    total_volume += volume
            else:
                # 卖盘：价格在center_price + width以下
                if price <= center_price + width:
                    total_volume += volume
        
        return total_volume
    
    def _calculate_momentum(self, current_tick, previous_tick):
        """计算1秒动量"""
        if not previous_tick:
            return 0
        
        # 计算价格宽度
        current_mid = current_tick['mid_price']
        previous_mid = previous_tick['mid_price']
        width = self._calculate_price_width(current_mid)
        
        # 计算当前tick在价格范围内的买卖量
        current_bid_volume = self._get_volume_in_range(
            current_tick['bid_prices'], 
            current_tick['bid_volumes'],
            current_mid, width, is_bid=True
        )
        
        current_ask_volume = self._get_volume_in_range(
            current_tick['ask_prices'],
            current_tick['ask_volumes'], 
            current_mid, width, is_bid=False
        )
        
        # 计算前一tick在价格范围内的买卖量
        previous_bid_volume = self._get_volume_in_range(
            previous_tick['bid_prices'],
            previous_tick['bid_volumes'],
            previous_mid, width, is_bid=True
        )
        
        previous_ask_volume = self._get_volume_in_range(
            previous_tick['ask_prices'],
            previous_tick['ask_volumes'],
            previous_mid, width, is_bid=False
        )
        
        # 计算动量：买量增加 + 卖量减少
        delta_bid = current_bid_volume - previous_bid_volume
        delta_ask = previous_ask_volume - current_ask_volume
        
        momentum = delta_bid + delta_ask
        
        # 标准化动量（除以总阻力）
        total_resistance = current_bid_volume + current_ask_volume
        if total_resistance > 0:
            normalized_momentum = momentum / total_resistance
        else:
            normalized_momentum = 0
            
        return normalized_momentum
    
    def calculate_momentum_signals(self, momentum_threshold=0.01):
        """计算动量信号"""
        print("开始计算1秒动量信号...")
        
        momentum_values = []
        long_signals = []
        short_signals = []
        
        for i in range(len(self.processed_data)):
            if i == 0:
                momentum = 0
            else:
                # 检查时间间隔是否在1秒窗口内
                time_diff = (self.processed_data[i]['timestamp'] - 
                           self.processed_data[i-1]['timestamp']).total_seconds()
                
                if time_diff <= self.momentum_window:
                    momentum = self._calculate_momentum(
                        self.processed_data[i], 
                        self.processed_data[i-1]
                    )
                else:
                    momentum = 0
            
            momentum_values.append(momentum)
            
            # 生成交易信号
            if momentum >= momentum_threshold:
                long_signals.append(1)
                short_signals.append(0)
            elif momentum <= -momentum_threshold:
                long_signals.append(0)
                short_signals.append(1)
            else:
                long_signals.append(0)
                short_signals.append(0)
        
        # 保存结果
        self.momentum_signals = {
            'momentum_values': momentum_values,
            'long_signals': long_signals,
            'short_signals': short_signals,
            'timestamps': [tick['timestamp'] for tick in self.processed_data],
            'symbols': [tick['symbol'] for tick in self.processed_data],
            'mid_prices': [tick['mid_price'] for tick in self.processed_data]
        }
        
        print(f"计算完成，生成{len(momentum_values)}个动量值")
        return self.momentum_signals
    
    def get_signal_statistics(self):
        """获取信号统计信息"""
        if not self.momentum_signals:
            print("请先计算动量信号")
            return None
        
        momentum_values = self.momentum_signals['momentum_values']
        long_signals = self.momentum_signals['long_signals']
        short_signals = self.momentum_signals['short_signals']
        
        stats = {
            'total_ticks': len(momentum_values),
            'momentum_mean': np.mean(momentum_values),
            'momentum_std': np.std(momentum_values),
            'momentum_max': np.max(momentum_values),
            'momentum_min': np.min(momentum_values),
            'long_signal_count': sum(long_signals),
            'short_signal_count': sum(short_signals),
            'signal_rate': (sum(long_signals) + sum(short_signals)) / len(momentum_values)
        }
        
        return stats
    
    def export_results(self, filename=None):
        """导出结果到CSV文件"""
        if not self.momentum_signals:
            print("请先计算动量信号")
            return None
        
        if filename is None:
            filename = f"one_second_momentum_{self.momentum_signals['symbols'][0]}_{datetime.now().strftime('%Y%m%d_%H%M%S')}.csv"
        
        # 创建结果DataFrame
        results_df = pd.DataFrame({
            'timestamp': self.momentum_signals['timestamps'],
            'symbol': self.momentum_signals['symbols'],
            'mid_price': self.momentum_signals['mid_prices'],
            'momentum': self.momentum_signals['momentum_values'],
            'long_signal': self.momentum_signals['long_signals'],
            'short_signal': self.momentum_signals['short_signals']
        })
        
        results_df.to_csv(filename, index=False)
        print(f"结果已导出到: {filename}")
        return filename

def load_level2_data(file_path=None, sample_data=None):
    """加载Level 2数据"""
    if sample_data is not None:
        # 使用提供的样本数据
        return sample_data
    elif file_path:
        # 从文件加载
        return pd.read_csv(file_path)
    else:
        raise ValueError("请提供数据文件路径或样本数据")

def main():
    """主函数示例"""
    # 使用提供的样本数据创建DataFrame
    sample_data = {
        'id': [1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19],
        'symbol': ['solusdt'] * 19,
        'timestamp': [
            '2025-4-27 8:02:03', '2025-4-27 8:02:27', '2025-4-27 8:02:56', 
            '2025-4-27 8:02:57', '2025-4-27 8:02:58', '2025-4-27 8:03:03',
            '2025-4-27 8:03:13', '2025-4-27 8:07:42', '2025-4-27 8:07:43',
            '2025-4-27 8:07:44', '2025-4-27 8:07:46', '2025-4-27 8:07:48',
            '2025-4-27 8:08:00', '2025-4-27 8:08:10', '2025-4-27 8:08:11',
            '2025-4-27 8:08:12', '2025-4-27 8:08:13', '2025-4-27 8:08:14',
            '2025-4-27 8:08:17'
        ],
        'bids_json': [
            '[[149.14, 1014.48], [149.13, 883.52], [149.12, 708.73]]',
            '[[149.15, 1383.59], [149.14, 1018.94], [149.13, 706.59]]',
            '[[149.25, 72.24], [149.24, 249.99], [149.23, 859.74]]'
        ] + ['[[149.25, 72.24], [149.24, 249.99], [149.23, 859.74]]'] * 16,
        'asks_json': [
            '[[149.15, 52.7], [149.16, 159.86], [149.17, 199.03]]',
            '[[149.16, 224.13], [149.17, 184.48], [149.18, 316.88]]',
            '[[149.26, 811.38], [149.27, 572.6], [149.28, 409.96]]'
        ] + ['[[149.26, 811.38], [149.27, 572.6], [149.28, 409.96]]'] * 16,
        'best_bid_price': [149.14, 149.15, 149.25] + [149.25] * 16,
        'best_ask_price': [149.15, 149.16, 149.26] + [149.26] * 16,
        'bid_volume': [28600.61, 28819.04, 19738.95] + [19738.95] * 16,
        'ask_volume': [13484.17, 13959.96, 17041.63] + [17041.63] * 16,
        'spread': [0.01] * 19,
        'spread_percent': [0.0067] * 19,
        'created_at': ['03:16.0'] * 19
    }
    
    # 创建DataFrame
    df = pd.DataFrame(sample_data)
    
    # 初始化处理器
    processor = OneSecondMomentumProcessor(df, momentum_window=1.0, price_width_percent=0.001)
    
    # 计算动量信号
    signals = processor.calculate_momentum_signals(momentum_threshold=0.01)
    
    # 获取统计信息
    stats = processor.get_signal_statistics()
    print("\n=== 1秒动量信号统计 ===")
    for key, value in stats.items():
        print(f"{key}: {value}")
    
    # 导出结果
    output_file = processor.export_results()
    
    return processor, signals, stats

if __name__ == "__main__":
    processor, signals, stats = main()
