# -*- coding: utf-8 -*-
import processData as pc
import pandas as pd
import signalEstimateTwap as set
import plateMovement as pm
import numpy as np
import filedata

def getDailyTest(path_csv,startTime, endTime,platePercent,ratioLow,ratioHigh,tWapDuration,signalDuration):

    stockData = pd.read_csv(path_csv).values
    askPriceList, bidPriceList, askVolumeList, bidVolumeList, volumeList, amountList, lastPriceList, timeStampList = pc.processStockData(
        stockData, startTime, endTime)


    plateMovementResearch = pm.plateMovementResearch(askPriceList, bidPriceList, askVolumeList, bidVolumeList, volumeList,
                                        amountList, lastPriceList, timeStampList, platePercent)


    plateMovementResearch.getRationalWidth() #price,width
    plateMovementResearch.getResist()#每条tick,10档盘口在width内的价格对应的的卖方挂单的量的和，组成的列表，买方同样方式生成列表
    plateMovementResearch.getMovement()#每条tick符合条件的卖量减少与买量增加的和，除以当前卖量的比例组成列表
    plateMovementResearch.getMovementSignal(ratioLow,ratioHigh)#tick动量比例（增加减少量与当前tick量的比值），组成的两个0，1列表

    longMovementSignalList = plateMovementResearch.longMovementSignalList#动量大于阈值为1，否则为0的列表
    shortMovementSignalList = plateMovementResearch.shortMovementSignalList#动量小于阈值为1，否则为0的列表

    signalEstimate = set.signalEstimate(askPriceList, bidPriceList, lastPriceList, tWapDuration, timeStampList)
    #tWapDuration，业绩比较基准，最小时间间隔，timeStampList,实例化类，并传参
    profitListLongMovement = signalEstimate.longSignalEstimateTwap(signalDuration, longMovementSignalList)#tickwap收益组成的列表
    profitListShortMovement = signalEstimate.shortSignalEstimateTwap(signalDuration, shortMovementSignalList)#tickwap收益组成的列表

    return profitListLongMovement, profitListShortMovement

def getTotalTest(startDate,endDate,stockCode,startTime, endTime,platePercent,ratioLow,ratioHigh,tWapDuration,signalDuration):

    file_list=filedata.path_name(startDate,endDate,stockCode)
    totalProfitListLongMovement = []
    totalProfitListShortMovement = []

    for path_csv in file_list:
        # try:
            profitListLongMovement, profitListShortMovement = getDailyTest(path_csv,startTime, endTime,platePercent,ratioLow,ratioHigh,tWapDuration,signalDuration)
        # except:
        #     continue
        # else:
            for n in profitListLongMovement:
                totalProfitListLongMovement.append(n)
            for n in profitListShortMovement:
                totalProfitListShortMovement.append(n)


    if len(totalProfitListLongMovement) ==0:
        meanLongMovement = 0
    else:
        meanLongMovement = np.mean(totalProfitListLongMovement)

    if len(totalProfitListShortMovement) ==0:
        meanShortMovement = 0
    else:
        meanShortMovement = np.mean(totalProfitListShortMovement)

    return meanLongMovement,len(totalProfitListLongMovement),meanShortMovement,len(totalProfitListShortMovement)

if __name__ == '__main__':

    stockCode = "000001.SZ"

    startTime = 93100
    endTime = 145000

    #动量值得范围
    ratioHigh = 10000.0
    ratioLow = 4.0

    #用于计算有效挂单的宽度
    platePercent = 0.002

    # 业绩比较基准，多少秒的tWap
    tWapDuration = 120

    # 最小采样间隔，单位是秒
    signalDuration = 60

    # 回测的开始结束日期
    startDate = 20190620
    endDate = 20190620

    meanLongMovement, lengthLongMovement, meanShortMovement, lengthShortMovement = getTotalTest(startDate, endDate, stockCode, startTime, endTime,platePercent,
                                                                ratioLow,ratioHigh,tWapDuration, signalDuration)
    print(meanLongMovement, lengthLongMovement, meanShortMovement, lengthShortMovement)
    #8.33891351778368 2 -2.3216968334897046 7

