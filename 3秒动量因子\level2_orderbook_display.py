"""
Level 2 数据盘口显示模块
支持实时显示买卖盘深度、价格变化和动量信号
"""

import pandas as pd
import numpy as np
import json
import matplotlib.pyplot as plt
import matplotlib.animation as animation
from datetime import datetime
import seaborn as sns
from one_second_momentum_processor import OneSecondMomentumProcessor

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['SimHei', 'Arial Unicode MS', 'DejaVu Sans']
plt.rcParams['axes.unicode_minus'] = False

class Level2OrderBookDisplay:
    
    def __init__(self, data_df, max_depth=10):
        """
        初始化Level 2盘口显示器
        
        Args:
            data_df: Level 2数据DataFrame
            max_depth: 显示的最大档位深度
        """
        self.data_df = data_df.copy()
        self.max_depth = max_depth
        self.current_index = 0
        
        # 预处理数据
        self._preprocess_data()
        
        # 初始化动量处理器
        self.momentum_processor = OneSecondMomentumProcessor(data_df)
        self.momentum_signals = self.momentum_processor.calculate_momentum_signals()
    
    def _preprocess_data(self):
        """预处理数据"""
        self.data_df['timestamp_dt'] = pd.to_datetime(self.data_df['timestamp'])
        self.data_df = self.data_df.sort_values('timestamp_dt').reset_index(drop=True)
        
        # 解析JSON数据
        self.processed_ticks = []
        for idx, row in self.data_df.iterrows():
            try:
                bids_data = json.loads(row['bids_json']) if isinstance(row['bids_json'], str) else row['bids_json']
                asks_data = json.loads(row['asks_json']) if isinstance(row['asks_json'], str) else row['asks_json']
                
                tick_data = {
                    'timestamp': row['timestamp_dt'],
                    'symbol': row['symbol'],
                    'bids': [(float(bid[0]), float(bid[1])) for bid in bids_data[:self.max_depth]],
                    'asks': [(float(ask[0]), float(ask[1])) for ask in asks_data[:self.max_depth]],
                    'best_bid': float(row['best_bid_price']),
                    'best_ask': float(row['best_ask_price']),
                    'spread': float(row['spread']),
                    'spread_percent': float(row['spread_percent'])
                }
                self.processed_ticks.append(tick_data)
            except Exception as e:
                print(f"处理数据错误: {e}")
                continue
    
    def display_current_orderbook(self, tick_index=0):
        """显示当前盘口快照"""
        if tick_index >= len(self.processed_ticks):
            print("索引超出范围")
            return
        
        tick = self.processed_ticks[tick_index]
        momentum = self.momentum_signals['momentum_values'][tick_index] if tick_index < len(self.momentum_signals['momentum_values']) else 0
        long_signal = self.momentum_signals['long_signals'][tick_index] if tick_index < len(self.momentum_signals['long_signals']) else 0
        short_signal = self.momentum_signals['short_signals'][tick_index] if tick_index < len(self.momentum_signals['short_signals']) else 0
        
        print(f"\n{'='*60}")
        print(f"时间: {tick['timestamp']}")
        print(f"品种: {tick['symbol'].upper()}")
        print(f"价差: {tick['spread']:.4f} ({tick['spread_percent']:.4f}%)")
        print(f"1秒动量: {momentum:.6f}")
        print(f"多头信号: {long_signal}, 空头信号: {short_signal}")
        print(f"{'='*60}")
        
        # 显示盘口
        print(f"{'档位':<4} {'卖量':<12} {'卖价':<10} {'买价':<10} {'买量':<12}")
        print("-" * 60)
        
        max_len = max(len(tick['asks']), len(tick['bids']))
        
        for i in range(max_len):
            ask_price = ask_volume = bid_price = bid_volume = ""
            
            if i < len(tick['asks']):
                ask_price = f"{tick['asks'][i][0]:.2f}"
                ask_volume = f"{tick['asks'][i][1]:.2f}"
            
            if i < len(tick['bids']):
                bid_price = f"{tick['bids'][i][0]:.2f}"
                bid_volume = f"{tick['bids'][i][1]:.2f}"
            
            print(f"{i+1:<4} {ask_volume:<12} {ask_price:<10} {bid_price:<10} {bid_volume:<12}")
    
    def plot_orderbook_depth(self, tick_index=0, save_path=None):
        """绘制盘口深度图"""
        if tick_index >= len(self.processed_ticks):
            print("索引超出范围")
            return
        
        tick = self.processed_ticks[tick_index]
        
        fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(15, 8))
        
        # 买盘深度
        bid_prices = [bid[0] for bid in tick['bids']]
        bid_volumes = [bid[1] for bid in tick['bids']]
        bid_cumulative = np.cumsum(bid_volumes)
        
        ax1.barh(bid_prices, bid_volumes, color='green', alpha=0.7, label='买盘量')
        ax1.plot(bid_cumulative, bid_prices, 'g-', linewidth=2, label='累计买盘')
        ax1.set_xlabel('数量')
        ax1.set_ylabel('价格')
        ax1.set_title('买盘深度')
        ax1.legend()
        ax1.grid(True, alpha=0.3)
        
        # 卖盘深度
        ask_prices = [ask[0] for ask in tick['asks']]
        ask_volumes = [ask[1] for ask in tick['asks']]
        ask_cumulative = np.cumsum(ask_volumes)
        
        ax2.barh(ask_prices, ask_volumes, color='red', alpha=0.7, label='卖盘量')
        ax2.plot(ask_cumulative, ask_prices, 'r-', linewidth=2, label='累计卖盘')
        ax2.set_xlabel('数量')
        ax2.set_ylabel('价格')
        ax2.set_title('卖盘深度')
        ax2.legend()
        ax2.grid(True, alpha=0.3)
        
        plt.suptitle(f'{tick["symbol"].upper()} 盘口深度 - {tick["timestamp"]}')
        plt.tight_layout()
        
        if save_path:
            plt.savefig(save_path, dpi=300, bbox_inches='tight')
        plt.show()
    
    def plot_price_momentum_timeline(self, save_path=None):
        """绘制价格和动量时间序列"""
        timestamps = [tick['timestamp'] for tick in self.processed_ticks]
        mid_prices = [(tick['best_bid'] + tick['best_ask']) / 2 for tick in self.processed_ticks]
        spreads = [tick['spread'] for tick in self.processed_ticks]
        
        momentum_values = self.momentum_signals['momentum_values']
        long_signals = self.momentum_signals['long_signals']
        short_signals = self.momentum_signals['short_signals']
        
        fig, ((ax1, ax2), (ax3, ax4)) = plt.subplots(2, 2, figsize=(16, 10))
        
        # 中间价走势
        ax1.plot(timestamps, mid_prices, 'b-', linewidth=1.5, label='中间价')
        ax1.set_title('价格走势')
        ax1.set_ylabel('价格')
        ax1.legend()
        ax1.grid(True, alpha=0.3)
        ax1.tick_params(axis='x', rotation=45)
        
        # 价差走势
        ax2.plot(timestamps, spreads, 'orange', linewidth=1.5, label='价差')
        ax2.set_title('价差走势')
        ax2.set_ylabel('价差')
        ax2.legend()
        ax2.grid(True, alpha=0.3)
        ax2.tick_params(axis='x', rotation=45)
        
        # 动量值
        ax3.plot(timestamps, momentum_values, 'purple', linewidth=1, label='1秒动量')
        ax3.axhline(y=0, color='black', linestyle='--', alpha=0.5)
        ax3.set_title('1秒动量')
        ax3.set_ylabel('动量值')
        ax3.legend()
        ax3.grid(True, alpha=0.3)
        ax3.tick_params(axis='x', rotation=45)
        
        # 交易信号
        signal_times = []
        signal_values = []
        signal_colors = []
        
        for i, (long, short) in enumerate(zip(long_signals, short_signals)):
            if long == 1:
                signal_times.append(timestamps[i])
                signal_values.append(1)
                signal_colors.append('green')
            elif short == 1:
                signal_times.append(timestamps[i])
                signal_values.append(-1)
                signal_colors.append('red')
        
        if signal_times:
            ax4.scatter(signal_times, signal_values, c=signal_colors, s=50, alpha=0.7)
        ax4.axhline(y=0, color='black', linestyle='-', alpha=0.5)
        ax4.set_title('交易信号')
        ax4.set_ylabel('信号 (1=多头, -1=空头)')
        ax4.set_ylim(-1.5, 1.5)
        ax4.grid(True, alpha=0.3)
        ax4.tick_params(axis='x', rotation=45)
        
        plt.suptitle(f'{self.processed_ticks[0]["symbol"].upper()} Level 2 数据分析')
        plt.tight_layout()
        
        if save_path:
            plt.savefig(save_path, dpi=300, bbox_inches='tight')
        plt.show()
    
    def generate_summary_report(self):
        """生成汇总报告"""
        total_ticks = len(self.processed_ticks)
        momentum_stats = self.momentum_processor.get_signal_statistics()
        
        # 价格统计
        mid_prices = [(tick['best_bid'] + tick['best_ask']) / 2 for tick in self.processed_ticks]
        spreads = [tick['spread'] for tick in self.processed_ticks]
        
        price_stats = {
            'price_mean': np.mean(mid_prices),
            'price_std': np.std(mid_prices),
            'price_min': np.min(mid_prices),
            'price_max': np.max(mid_prices),
            'spread_mean': np.mean(spreads),
            'spread_std': np.std(spreads)
        }
        
        print("\n" + "="*50)
        print("Level 2 数据分析报告")
        print("="*50)
        print(f"数据时间范围: {self.processed_ticks[0]['timestamp']} 至 {self.processed_ticks[-1]['timestamp']}")
        print(f"总tick数: {total_ticks}")
        print(f"品种: {self.processed_ticks[0]['symbol'].upper()}")
        
        print("\n价格统计:")
        print(f"  平均价格: {price_stats['price_mean']:.4f}")
        print(f"  价格标准差: {price_stats['price_std']:.4f}")
        print(f"  价格范围: {price_stats['price_min']:.4f} - {price_stats['price_max']:.4f}")
        print(f"  平均价差: {price_stats['spread_mean']:.4f}")
        print(f"  价差标准差: {price_stats['spread_std']:.4f}")
        
        print("\n动量信号统计:")
        for key, value in momentum_stats.items():
            print(f"  {key}: {value}")
        
        return {**price_stats, **momentum_stats}

def demo_with_sample_data():
    """使用样本数据进行演示"""
    # 创建样本数据
    sample_data = {
        'id': list(range(1, 20)),
        'symbol': ['solusdt'] * 19,
        'timestamp': [
            '2025-4-27 8:02:03', '2025-4-27 8:02:27', '2025-4-27 8:02:56', 
            '2025-4-27 8:02:57', '2025-4-27 8:02:58', '2025-4-27 8:03:03',
            '2025-4-27 8:03:13', '2025-4-27 8:07:42', '2025-4-27 8:07:43',
            '2025-4-27 8:07:44', '2025-4-27 8:07:46', '2025-4-27 8:07:48',
            '2025-4-27 8:08:00', '2025-4-27 8:08:10', '2025-4-27 8:08:11',
            '2025-4-27 8:08:12', '2025-4-27 8:08:13', '2025-4-27 8:08:14',
            '2025-4-27 8:08:17'
        ],
        'bids_json': [
            '[[149.14, 1014.48], [149.13, 883.52], [149.12, 708.73], [149.11, 1385.65], [149.1, 850.55]]',
            '[[149.15, 1383.59], [149.14, 1018.94], [149.13, 706.59], [149.12, 794.58], [149.11, 1179.99]]',
            '[[149.25, 72.24], [149.24, 249.99], [149.23, 859.74], [149.22, 772.93], [149.21, 643.82]]'
        ] + ['[[149.25, 72.24], [149.24, 249.99], [149.23, 859.74], [149.22, 772.93], [149.21, 643.82]]'] * 16,
        'asks_json': [
            '[[149.15, 52.7], [149.16, 159.86], [149.17, 199.03], [149.18, 374.12], [149.19, 407.73]]',
            '[[149.16, 224.13], [149.17, 184.48], [149.18, 316.88], [149.19, 381.1], [149.2, 657.34]]',
            '[[149.26, 811.38], [149.27, 572.6], [149.28, 409.96], [149.29, 610.83], [149.3, 1550.74]]'
        ] + ['[[149.26, 811.38], [149.27, 572.6], [149.28, 409.96], [149.29, 610.83], [149.3, 1550.74]]'] * 16,
        'best_bid_price': [149.14, 149.15, 149.25] + [149.25] * 16,
        'best_ask_price': [149.15, 149.16, 149.26] + [149.26] * 16,
        'bid_volume': [28600.61, 28819.04, 19738.95] + [19738.95] * 16,
        'ask_volume': [13484.17, 13959.96, 17041.63] + [17041.63] * 16,
        'spread': [0.01] * 19,
        'spread_percent': [0.0067] * 19,
        'created_at': ['03:16.0'] * 19
    }
    
    df = pd.DataFrame(sample_data)
    
    # 创建显示器
    display = Level2OrderBookDisplay(df, max_depth=5)
    
    # 显示第一个盘口快照
    print("显示第一个盘口快照:")
    display.display_current_orderbook(0)
    
    # 生成汇总报告
    report = display.generate_summary_report()
    
    # 绘制图表
    print("\n正在生成图表...")
    display.plot_orderbook_depth(0)
    display.plot_price_momentum_timeline()
    
    return display, report

if __name__ == "__main__":
    display, report = demo_with_sample_data()
