"""
Level 2 数据分析主程序
整合1秒动量计算和盘口显示功能

使用方法:
1. 准备Level 2数据CSV文件，包含以下列：
   id, symbol, timestamp, update_id, bids_json, asks_json, 
   best_bid_price, best_ask_price, bid_volume, ask_volume, 
   spread, spread_percent, created_at

2. 运行程序进行分析
"""

import pandas as pd
import numpy as np
import argparse
import os
from datetime import datetime
import warnings
warnings.filterwarnings('ignore')

from one_second_momentum_processor import OneSecondMomentumProcessor
from level2_orderbook_display import Level2OrderBookDisplay

class Level2AnalysisMain:
    
    def __init__(self, data_source=None, config=None):
        """
        初始化Level 2分析主程序
        
        Args:
            data_source: 数据源，可以是文件路径或DataFrame
            config: 配置参数字典
        """
        self.config = config or self._default_config()
        self.data_df = None
        self.momentum_processor = None
        self.display = None
        self.results = {}
        
        if data_source is not None:
            self.load_data(data_source)
    
    def _default_config(self):
        """默认配置参数"""
        return {
            'momentum_window': 1.0,  # 动量计算窗口(秒)
            'price_width_percent': 0.001,  # 价格宽度百分比
            'momentum_threshold': 0.01,  # 动量信号阈值
            'max_depth': 10,  # 最大显示档位
            'output_dir': 'output',  # 输出目录
            'save_plots': True,  # 是否保存图表
            'save_results': True,  # 是否保存结果（强制开启）
            'force_save': True  # 强制保存所有结果
        }
    
    def load_data(self, data_source):
        """加载数据"""
        if isinstance(data_source, str):
            # 从文件加载
            if not os.path.exists(data_source):
                raise FileNotFoundError(f"数据文件不存在: {data_source}")
            
            print(f"正在加载数据文件: {data_source}")
            self.data_df = pd.read_csv(data_source)
            print(f"成功加载{len(self.data_df)}条数据")
            
        elif isinstance(data_source, pd.DataFrame):
            # 直接使用DataFrame
            self.data_df = data_source.copy()
            print(f"使用提供的DataFrame，包含{len(self.data_df)}条数据")
            
        else:
            raise ValueError("数据源必须是文件路径或pandas DataFrame")
        
        # 验证数据格式
        self._validate_data()
    
    def _validate_data(self):
        """验证数据格式"""
        required_columns = [
            'symbol', 'timestamp', 'bids_json', 'asks_json',
            'best_bid_price', 'best_ask_price', 'bid_volume', 'ask_volume'
        ]
        
        missing_columns = [col for col in required_columns if col not in self.data_df.columns]
        if missing_columns:
            raise ValueError(f"数据缺少必要列: {missing_columns}")
        
        print("数据格式验证通过")
    
    def run_momentum_analysis(self):
        """运行动量分析"""
        print("\n开始1秒动量分析...")
        
        self.momentum_processor = OneSecondMomentumProcessor(
            self.data_df,
            momentum_window=self.config['momentum_window'],
            price_width_percent=self.config['price_width_percent']
        )
        
        # 计算动量信号
        momentum_signals = self.momentum_processor.calculate_momentum_signals(
            momentum_threshold=self.config['momentum_threshold']
        )
        
        # 获取统计信息
        momentum_stats = self.momentum_processor.get_signal_statistics()
        
        self.results['momentum_signals'] = momentum_signals
        self.results['momentum_stats'] = momentum_stats
        
        print("动量分析完成")
        return momentum_signals, momentum_stats
    
    def run_orderbook_analysis(self):
        """运行盘口分析"""
        print("\n开始盘口分析...")
        
        self.display = Level2OrderBookDisplay(
            self.data_df,
            max_depth=self.config['max_depth']
        )
        
        # 生成汇总报告
        summary_report = self.display.generate_summary_report()
        self.results['summary_report'] = summary_report
        
        print("盘口分析完成")
        return summary_report
    
    def generate_visualizations(self):
        """生成可视化图表"""
        if self.display is None:
            print("请先运行盘口分析")
            return
        
        print("\n生成可视化图表...")
        
        # 创建输出目录
        if self.config['save_plots']:
            os.makedirs(self.config['output_dir'], exist_ok=True)
            timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
            
            # 盘口深度图
            depth_path = os.path.join(self.config['output_dir'], f'orderbook_depth_{timestamp}.png')
            self.display.plot_orderbook_depth(0, save_path=depth_path)
            
            # 价格动量时间序列图
            timeline_path = os.path.join(self.config['output_dir'], f'price_momentum_timeline_{timestamp}.png')
            self.display.plot_price_momentum_timeline(save_path=timeline_path)
            
            print(f"图表已保存到: {self.config['output_dir']}")
        else:
            # 只显示不保存
            self.display.plot_orderbook_depth(0)
            self.display.plot_price_momentum_timeline()
    
    def save_results(self):
        """保存分析结果"""
        print("\n保存分析结果...")

        # 创建输出目录
        os.makedirs(self.config['output_dir'], exist_ok=True)
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')

        saved_files = []

        # 保存动量信号结果
        if self.momentum_processor:
            momentum_file = os.path.join(self.config['output_dir'], f'momentum_results_{timestamp}.csv')
            self.momentum_processor.export_results(momentum_file)
            saved_files.append(momentum_file)

        # 保存详细分析结果CSV
        if 'momentum_signals' in self.results:
            detailed_file = os.path.join(self.config['output_dir'], f'detailed_analysis_{timestamp}.csv')
            self._save_detailed_results_csv(detailed_file)
            saved_files.append(detailed_file)

        # 保存汇总统计CSV
        if 'summary_report' in self.results:
            summary_csv_file = os.path.join(self.config['output_dir'], f'summary_stats_{timestamp}.csv')
            self._save_summary_stats_csv(summary_csv_file)
            saved_files.append(summary_csv_file)

            # 保存汇总统计文本报告
            stats_file = os.path.join(self.config['output_dir'], f'analysis_summary_{timestamp}.txt')
            with open(stats_file, 'w', encoding='utf-8') as f:
                f.write("Level 2 数据分析汇总报告\n")
                f.write("=" * 50 + "\n")
                f.write(f"分析时间: {datetime.now()}\n\n")

                if 'momentum_stats' in self.results:
                    f.write("动量信号统计:\n")
                    for key, value in self.results['momentum_stats'].items():
                        f.write(f"  {key}: {value}\n")
                    f.write("\n")

                f.write("价格统计:\n")
                for key, value in self.results['summary_report'].items():
                    f.write(f"  {key}: {value}\n")
            saved_files.append(stats_file)

        print(f"分析结果已保存到: {self.config['output_dir']}")
        print("保存的文件:")
        for file in saved_files:
            print(f"  - {file}")

        return saved_files

    def _save_detailed_results_csv(self, filename):
        """保存详细分析结果到CSV"""
        try:
            momentum_signals = self.results['momentum_signals']

            # 创建详细结果DataFrame
            detailed_df = pd.DataFrame({
                'timestamp': momentum_signals['timestamps'],
                'symbol': momentum_signals['symbols'],
                'mid_price': momentum_signals['mid_prices'],
                'momentum_value': momentum_signals['momentum_values'],
                'long_signal': momentum_signals['long_signals'],
                'short_signal': momentum_signals['short_signals'],
                'signal_type': ['Long' if l else 'Short' if s else 'None'
                               for l, s in zip(momentum_signals['long_signals'], momentum_signals['short_signals'])],
                'abs_momentum': [abs(m) for m in momentum_signals['momentum_values']]
            })

            # 添加额外的分析列
            detailed_df['price_change'] = detailed_df['mid_price'].diff()
            detailed_df['price_change_pct'] = detailed_df['mid_price'].pct_change() * 100
            detailed_df['momentum_rank'] = detailed_df['momentum_value'].rank(pct=True)

            detailed_df.to_csv(filename, index=False, encoding='utf-8')
            print(f"✓ 详细分析结果已保存: {filename}")

        except Exception as e:
            print(f"保存详细结果时出错: {e}")

    def _save_summary_stats_csv(self, filename):
        """保存汇总统计到CSV"""
        try:
            # 合并所有统计信息
            all_stats = {}
            if 'momentum_stats' in self.results:
                all_stats.update(self.results['momentum_stats'])
            if 'summary_report' in self.results:
                all_stats.update(self.results['summary_report'])

            # 创建统计DataFrame
            stats_df = pd.DataFrame([
                {'metric': key, 'value': value, 'category': self._get_metric_category(key)}
                for key, value in all_stats.items()
            ])

            stats_df.to_csv(filename, index=False, encoding='utf-8')
            print(f"✓ 汇总统计已保存: {filename}")

        except Exception as e:
            print(f"保存汇总统计时出错: {e}")

    def _get_metric_category(self, metric_name):
        """获取指标分类"""
        if 'momentum' in metric_name.lower():
            return 'momentum'
        elif 'signal' in metric_name.lower():
            return 'signal'
        elif 'price' in metric_name.lower():
            return 'price'
        elif 'spread' in metric_name.lower():
            return 'spread'
        else:
            return 'other'

    def run_full_analysis(self):
        """运行完整分析流程"""
        if self.data_df is None:
            raise ValueError("请先加载数据")

        print("开始完整Level 2数据分析...")

        # 1. 动量分析
        self.run_momentum_analysis()

        # 2. 盘口分析
        self.run_orderbook_analysis()

        # 3. 生成可视化
        self.generate_visualizations()

        # 4. 强制保存结果到CSV
        saved_files = self.save_results()

        print("\n✅ 分析完成！")
        print(f"📊 结果已保存到 {len(saved_files)} 个文件")

        return self.results, saved_files
    
    def display_sample_orderbook(self, tick_index=0):
        """显示样本盘口"""
        if self.display is None:
            self.run_orderbook_analysis()
        
        self.display.display_current_orderbook(tick_index)

def create_sample_data():
    """创建样本数据用于测试"""
    df = pd.read_csv(r'../order_books_1.csv')
    return df
    


def main():
    """主函数"""
    parser = argparse.ArgumentParser(description='Level 2 数据分析程序')
    parser.add_argument('--data', type=str, help='数据文件路径')
    parser.add_argument('--output', type=str, default='output', help='输出目录')
    parser.add_argument('--demo', action='store_true', help='使用样本数据演示')
    parser.add_argument('--no-plots', action='store_true', help='不生成图表')

    args = parser.parse_args()

    # 配置参数 - 强制保存结果
    config = {
        'momentum_window': 1.0,
        'price_width_percent': 0.001,
        'momentum_threshold': 0.01,
        'max_depth': 10,
        'output_dir': args.output,
        'save_plots': not args.no_plots,  # 可选择不生成图表
        'save_results': True,  # 强制保存结果
        'force_save': True
    }

    # 创建分析器
    if args.demo or args.data is None:
        print("🚀 使用样本数据进行演示...")
        sample_df = create_sample_data()
        analyzer = Level2AnalysisMain(sample_df, config)
    else:
        print(f"📁 加载数据文件: {args.data}")
        analyzer = Level2AnalysisMain(args.data, config)

    # 运行分析
    results, saved_files = analyzer.run_full_analysis()

    # 显示样本盘口
    print("\n📊 显示第一个盘口快照:")
    analyzer.display_sample_orderbook(0)

    # 显示保存的文件信息
    print(f"\n📁 共保存了 {len(saved_files)} 个结果文件:")
    for i, file in enumerate(saved_files, 1):
        file_size = os.path.getsize(file) if os.path.exists(file) else 0
        print(f"  {i}. {os.path.basename(file)} ({file_size} bytes)")

    return analyzer, results, saved_files

if __name__ == "__main__":
    analyzer, results = main()
